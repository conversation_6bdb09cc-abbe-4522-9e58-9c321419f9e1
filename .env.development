# CloudDrive 开发环境配置
# 复制此文件到 .env.local 并修改相应的值

# 应用基础配置
VITE_APP_TITLE=CloudDriveDev
VITE_APP_VERSION=1.0.0

# API 配置
VITE_API_BASE_URL=/api
VITE_API_TIMEOUT=30000

# 登录认证中心地址
VITE_AUTH_URL=http://test.cas.sh9130.com

# TUS 上传配置
VITE_TUS_ENDPOINT=http://172.20.22.137:8080/files
VITE_TUS_CHUNK_SIZE=20971520
VITE_TUS_PARALLEL_UPLOADS=5

# 下载配置
VITE_DOWNLOAD_ENDPOINT=http://172.20.22.137:8000
VITE_DOWNLOAD_CHUNK_SIZE=20971520

# 预览配置
VITE_PREVIEW_ENDPOINT=http://172.20.22.137:8000

# 智能打包配置
VITE_SMART_PACKING_THRESHOLD=10

# 开发配置
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEBUG=true
