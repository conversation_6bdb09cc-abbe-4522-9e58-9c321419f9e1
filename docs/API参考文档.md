# 星图云盘 API 参考文档

## 1. Electron API 概览

### 1.1 主要API模块

星图云盘通过 `window.electronAPI` 暴露以下核心API模块：

- **tus**: TUS上传相关API
- **download**: 文件下载相关API  
- **archive**: 文件压缩相关API
- **extraction**: 文件解压相关API
- **dragDrop**: 拖拽文件处理API
- **folderSelect**: 文件夹选择API

## 2. TUS上传API

### 2.1 核心方法

#### createUpload(filePath, metadata?)
创建上传任务（推荐用于大文件）

```typescript
const result = await window.electronAPI.tus.createUpload(
  '/path/to/file.txt',
  { categoryId: '123', parentId: '456' }
);
// 返回: { success: boolean, taskId?: string, error?: string }
```

#### createUploadFromFile(fileData)
从File对象创建上传任务

```typescript
const result = await window.electronAPI.tus.createUploadFromFile({
  name: file.name,
  content: await file.arrayBuffer(),
  metadata: { categoryId: '123' }
});
```

#### createUploadFromDialog()
通过文件选择对话框创建上传任务

```typescript
const taskIds = await window.electronAPI.tus.createUploadFromDialog();
// 返回: string[] - 任务ID数组
```

#### startUpload(taskId)
开始上传任务

```typescript
await window.electronAPI.tus.startUpload(taskId);
```

#### pauseUpload(taskId)
暂停上传任务

```typescript
await window.electronAPI.tus.pauseUpload(taskId);
```

#### resumeUpload(taskId)
恢复上传任务

```typescript
await window.electronAPI.tus.resumeUpload(taskId);
```

#### cancelUpload(taskId)
取消上传任务

```typescript
await window.electronAPI.tus.cancelUpload(taskId);
```

### 2.2 事件监听

#### onUploadTaskCreated(callback)
监听上传任务创建事件

```typescript
window.electronAPI.tus.onUploadTaskCreated((taskId, task) => {
  console.log('任务创建:', taskId, task);
});
```

#### onUploadTaskProgress(callback)
监听上传进度事件

```typescript
window.electronAPI.tus.onUploadTaskProgress((taskId, progress, bytesUploaded, bytesTotal) => {
  console.log(`任务 ${taskId} 进度: ${progress}%`);
});
```

#### onUploadTaskCompleted(callback)
监听上传完成事件

```typescript
window.electronAPI.tus.onUploadTaskCompleted((taskId, task) => {
  console.log('上传完成:', taskId);
});
```

#### onUploadTaskError(callback)
监听上传错误事件

```typescript
window.electronAPI.tus.onUploadTaskError((taskId, error) => {
  console.error('上传错误:', taskId, error);
});
```

## 3. 文件下载API

### 3.1 核心方法

#### createDownload(config)
创建下载任务

```typescript
const result = await window.electronAPI.download.createDownload({
  url: 'http://example.com/file.zip',
  savePath: '/path/to/save/file.zip',
  fileName: 'file.zip'
});
```

#### startDownload(taskId)
开始下载任务

```typescript
await window.electronAPI.download.startDownload(taskId);
```

#### pauseDownload(taskId)
暂停下载任务

```typescript
await window.electronAPI.download.pauseDownload(taskId);
```

#### resumeDownload(taskId)
恢复下载任务

```typescript
await window.electronAPI.download.resumeDownload(taskId);
```

### 3.2 事件监听

#### onDownloadTaskProgress(callback)
监听下载进度事件

```typescript
window.electronAPI.download.onDownloadTaskProgress((taskId, progress, bytesDownloaded, bytesTotal) => {
  console.log(`下载进度: ${progress}%`);
});
```

#### onDownloadTaskCompleted(callback)
监听下载完成事件

```typescript
window.electronAPI.download.onDownloadTaskCompleted((taskId, task) => {
  console.log('下载完成:', taskId);
});
```

## 4. 文件压缩API

### 4.1 核心方法

#### createArchive(filePaths, options?)
创建压缩任务

```typescript
const result = await window.electronAPI.archive.createArchive(
  ['/path/to/file1.txt', '/path/to/file2.txt'],
  {
    archiveName: 'my-archive.7z',
    compressionLevel: 0
  }
);
```

#### getArchiveTasks()
获取所有压缩任务

```typescript
const tasks = await window.electronAPI.archive.getArchiveTasks();
```

### 4.2 事件监听

#### onArchiveTaskProgress(callback)
监听压缩进度事件

```typescript
window.electronAPI.archive.onArchiveTaskProgress((taskId, progress) => {
  console.log(`压缩进度: ${progress}%`);
});
```

## 5. 文件解压API

### 5.1 核心方法

#### extractArchive(archivePath, extractPath)
解压文件

```typescript
const result = await window.electronAPI.extraction.extractArchive(
  '/path/to/archive.7z',
  '/path/to/extract/'
);
```

#### getExtractionTasks()
获取所有解压任务

```typescript
const tasks = await window.electronAPI.extraction.getExtractionTasks();
```

### 5.2 事件监听

#### onExtractionTaskProgress(callback)
监听解压进度事件

```typescript
window.electronAPI.extraction.onExtractionTaskProgress((taskId, progress) => {
  console.log(`解压进度: ${progress}%`);
});
```

## 6. 工具API

### 6.1 拖拽处理API

#### handleFileDrop(filePaths)
处理拖拽文件

```typescript
const result = await window.electronAPI.dragDrop.handleFileDrop([
  '/path/to/file1.txt',
  '/path/to/file2.txt'
]);
```

### 6.2 文件夹选择API

#### selectFolderAndGetFiles()
选择文件夹并获取文件列表

```typescript
const result = await window.electronAPI.folderSelect.selectFolderAndGetFiles();
// 返回: { cancelled: boolean, filePaths?: string[] }
```

### 6.3 系统API

#### getAppVersion()
获取应用版本

```typescript
const version = await window.electronAPI.getAppVersion();
```

#### getPlatform()
获取操作系统平台

```typescript
const platform = await window.electronAPI.getPlatform();
```

#### showOpenDialog(options)
显示文件打开对话框

```typescript
const result = await window.electronAPI.showOpenDialog({
  properties: ['openFile', 'multiSelections'],
  filters: [
    { name: 'Images', extensions: ['jpg', 'png', 'gif'] }
  ]
});
```

## 7. 类型定义

### 7.1 上传任务类型

```typescript
interface UploadTask {
  id: string;
  filePath: string;
  fileName: string;
  fileSize: number;
  progress: number;
  status: 'pending' | 'uploading' | 'paused' | 'completed' | 'error';
  bytesUploaded: number;
  uploadSpeed: number;
  remainingTime: number;
  startTime: Date;
  metadata?: Record<string, string>;
  resumable: boolean;
  isFolder?: boolean;
  isEmpty?: boolean;
}
```

### 7.2 下载任务类型

```typescript
interface DownloadTask {
  id: string;
  url: string;
  savePath: string;
  fileName: string;
  fileSize: number;
  progress: number;
  status: 'pending' | 'downloading' | 'paused' | 'completed' | 'error';
  bytesDownloaded: number;
  downloadSpeed: number;
  remainingTime: number;
  startTime: Date;
}
```

### 7.3 API响应类型

```typescript
interface ApiResponse {
  success: boolean;
  taskId?: string;
  error?: string;
  data?: any;
}
```

## 8. 使用示例

### 8.1 完整上传流程

```typescript
// 1. 创建上传任务
const result = await window.electronAPI.tus.createUploadFromDialog();

if (result.length > 0) {
  // 2. 监听进度
  window.electronAPI.tus.onUploadTaskProgress((taskId, progress) => {
    console.log(`上传进度: ${progress}%`);
  });
  
  // 3. 开始上传
  for (const taskId of result) {
    await window.electronAPI.tus.startUpload(taskId);
  }
}
```

### 8.2 完整下载流程

```typescript
// 1. 创建下载任务
const result = await window.electronAPI.download.createDownload({
  url: 'http://example.com/file.zip',
  savePath: '/Users/<USER>/Downloads/file.zip',
  fileName: 'file.zip'
});

if (result.success) {
  // 2. 监听进度
  window.electronAPI.download.onDownloadTaskProgress((taskId, progress) => {
    console.log(`下载进度: ${progress}%`);
  });
  
  // 3. 开始下载
  await window.electronAPI.download.startDownload(result.taskId);
}
```

---

**注意事项**:
1. 所有API调用都是异步的，需要使用await或Promise处理
2. 事件监听器需要在组件卸载时清理，避免内存泄漏
3. 大文件操作建议使用文件路径而非File对象
4. 错误处理应该包装在try-catch块中
