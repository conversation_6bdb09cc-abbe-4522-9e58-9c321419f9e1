# 星图 (CloudDrive) 技术交接文档

## 1. 项目概览与架构

### 1.1 项目简介
星图是一款基于 **Electron + Vue 3 + TypeScript** 构建的现代化云盘桌面应用程序，专为企业级文件管理和高效传输而设计。

### 1.2 核心技术栈
- **前端框架**: Vue 3 (Composition API) + TypeScript
- **桌面应用**: Electron 28.0.0
- **UI组件库**: shadcn-vue + Tailwind CSS
- **状态管理**: Pinia + pinia-plugin-persistedstate
- **文件传输**: tus-js-client (TUS协议) + StreamSaver (流式下载)
- **压缩处理**: node-7z + 7zip-bin
- **构建工具**: Vite + electron-builder
- **包管理器**: pnpm

### 1.3 整体架构设计理念

项目采用**分层架构**设计，将功能模块化，实现高内聚低耦合：

```
┌─────────────────────────────────────────────────────────────┐
│                    Vue 3 渲染进程 (Frontend)                │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   UI Components │ │   Composables   │ │  Pinia Stores   │ │
│  │   (shadcn-vue)  │ │  (业务逻辑层)    │ │   (状态管理)     │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │ IPC通信
┌─────────────────────────────────────────────────────────────┐
│                   Electron 主进程 (Backend)                │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   TUS Upload    │ │ Stream Download │ │ Archive/Extract │ │
│  │     Module      │ │     Module      │ │     Module      │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │ File System & Network
┌─────────────────────────────────────────────────────────────┐
│                      操作系统 & 网络                        │
└─────────────────────────────────────────────────────────────┘
```

### 1.4 主要功能模块概述

- **文件上传**: 基于TUS协议的断点续传上传，支持智能打包（≥10个文件自动7z压缩）
- **文件下载**: 基于StreamSaver的流式下载，支持断点续传和并发控制
- **文件管理**: 完整的云盘文件管理功能（增删改查、移动、重命名等）
- **压缩解压**: 自动7z压缩打包和下载后自动解压功能
- **认证系统**: 集成CAS单点登录认证
- **自动更新**: 基于electron-updater的应用自动更新

## 2. 项目目录结构

### 2.1 完整目录树结构

```
cloudDrive/
├── electron/                    # Electron主进程核心模块
│   ├── main.ts                 # 主进程入口文件
│   ├── preload.ts              # 预加载脚本，暴露API给渲染进程
│   ├── tus/                    # TUS上传模块
│   │   ├── index.ts            # 模块入口和工厂函数
│   │   ├── uploadManager.ts    # 上传管理器核心逻辑
│   │   ├── ipcHandlers.ts      # IPC处理器
│   │   ├── preloadApi.ts       # 预加载API封装
│   │   ├── types.ts            # 类型定义
│   │   └── logger.ts           # 日志记录器
│   ├── stream-downloader/      # 流式下载模块
│   │   ├── index.ts            # 模块入口
│   │   ├── downloadManager.ts  # 下载管理器
│   │   ├── ipcHandlers.ts      # IPC处理器
│   │   └── types.ts            # 类型定义
│   ├── archive/                # 压缩模块
│   │   ├── index.ts            # 模块入口
│   │   ├── archiveManager.ts   # 压缩管理器
│   │   └── types.ts            # 类型定义
│   ├── 7z-extractor/          # 解压缩模块
│   │   ├── index.ts            # 模块入口
│   │   ├── extractionManager.ts # 解压管理器
│   │   ├── worker.ts           # 后台工作进程
│   │   └── types.ts            # 类型定义
│   ├── auth/                   # 认证模块
│   ├── auto-updater/           # 自动更新模块
│   └── logger/                 # 日志模块
├── src/                        # Vue前端源码
│   ├── main.ts                 # Vue应用入口
│   ├── App.vue                 # 根组件
│   ├── components/             # 组件目录
│   │   ├── ui/                 # shadcn-vue基础UI组件
│   │   ├── Upload/             # 上传相关组件
│   │   │   ├── UploadDialog.vue      # 上传对话框
│   │   │   ├── FileUploadArea.vue    # 文件上传区域
│   │   │   └── composables/          # 上传相关组合函数
│   │   │       ├── useTusUpload.ts   # TUS上传逻辑
│   │   │       └── useUploadStrategy.ts # 上传策略
│   │   ├── Sidebar.vue         # 侧边栏
│   │   ├── Navigation.vue      # 导航栏
│   │   └── GlobalProgressIndicator/ # 全局进度指示器
│   ├── composables/            # 组合式函数
│   │   ├── useFileDownload.ts  # 文件下载逻辑
│   │   ├── useStreamDownload.ts # 流式下载逻辑
│   │   ├── useDirectoryStructure.ts # 目录结构管理
│   │   ├── useAuthGuard.ts     # 认证守卫
│   │   └── useDragAndDrop.ts   # 拖拽功能
│   ├── store/                  # Pinia状态管理
│   │   ├── sidebar.ts          # 侧边栏状态
│   │   └── auth.ts             # 认证状态
│   ├── api/                    # API服务层
│   │   ├── index.ts            # API统一入口
│   │   ├── http.ts             # HTTP客户端封装
│   │   └── services/           # API服务
│   │       ├── auth.ts         # 认证API
│   │       └── files.ts        # 文件管理API
│   ├── router/                 # 路由配置
│   │   └── index.ts            # 路由定义
│   ├── views/                  # 页面组件
│   │   ├── Login.vue           # 登录页
│   │   ├── Resources.vue       # 资源管理页
│   │   ├── Folders/            # 文件夹相关页面
│   │   └── Trash.vue           # 回收站页
│   ├── config/                 # 配置文件
│   │   └── index.ts            # 应用配置
│   ├── lib/                    # 工具库
│   │   ├── utils.ts            # 通用工具函数
│   │   └── upload-utils.ts     # 上传相关工具
│   └── types/                  # 类型定义
│       ├── electron.d.ts       # Electron类型声明
│       └── files.ts            # 文件相关类型
├── docs/                       # 文档目录
├── public/                     # 静态资源
├── dist/                       # 前端构建输出
├── dist-electron/              # Electron构建输出
├── release/                    # 应用发布包
├── package.json                # 项目配置和依赖
├── vite.config.ts              # Vite构建配置
├── electron-builder.yml        # Electron打包配置
├── tsconfig.json               # TypeScript配置
├── tailwind.config.js          # Tailwind CSS配置
└── .env.development            # 开发环境变量
```

### 2.2 关键配置文件说明

- **package.json**: 项目依赖和脚本配置，强制使用pnpm包管理器
- **vite.config.ts**: Vite构建配置，包含Electron插件配置和代理设置
- **electron-builder.yml**: Electron应用打包配置，支持Windows和macOS
- **tsconfig.json**: TypeScript编译配置，包含路径别名设置
- **.env.development**: 开发环境变量，包含API端点、TUS配置等
- **components.json**: shadcn-vue组件库配置文件

## 3. 核心模块详细分析

### 3.1 Electron主进程模块

#### 3.1.1 TUS上传模块 (electron/tus/)

**设计思路**: 基于TUS协议实现可靠的断点续传上传，支持大文件分片上传和智能打包。

**工作流程**:
```mermaid
graph TD
    A[文件选择] --> B{文件数量检查}
    B -->|≥10个文件| C[智能打包]
    B -->|<10个文件| D[直接上传]
    C --> E[7z压缩]
    E --> F[创建上传任务]
    D --> F
    F --> G[TUS分片上传]
    G --> H[进度回调]
    H --> I[上传完成]
```

**关键实现**:
- **TusUploadManager**: 核心上传管理器，管理任务生命周期
- **任务队列**: 支持并发上传控制（默认5个并发）
- **断点续传**: 基于TUS协议的可靠续传机制
- **智能打包**: 文件数量≥10时自动触发7z压缩（压缩级别0，仅存储）

#### 3.1.2 文件下载模块 (electron/stream-downloader/)

**设计思路**: 基于StreamSaver实现流式下载，避免大文件内存占用问题。

**工作流程**:
```mermaid
graph TD
    A[下载请求] --> B[路径选择]
    B --> C[创建下载任务]
    C --> D[流式下载]
    D --> E[进度更新]
    E --> F{下载完成}
    F -->|是| G[自动解压检查]
    F -->|否| D
    G --> H[完成]
```

**关键实现**:
- **StreamDownloadManager**: 下载管理器，支持并发控制（默认10个并发）
- **断点续传**: 支持下载中断后的恢复功能
- **自动解压**: 下载完成后自动检测并解压7z文件

#### 3.1.3 文件压缩模块 (electron/archive/)

**设计思路**: 集成7z压缩功能，为智能打包提供支持。

**关键实现**:
- **ArchiveManager**: 压缩管理器，使用node-7z库
- **智能打包**: 当上传文件数量≥10时自动触发
- **压缩配置**: 压缩级别0（仅存储），优化传输速度

#### 3.1.4 文件解压模块 (electron/7z-extractor/)

**设计思路**: 后台工作进程处理解压任务，避免阻塞主进程。

**关键实现**:
- **ExtractionManager**: 解压管理器
- **Worker进程**: 独立工作进程处理解压任务
- **自动解压**: 下载完成后自动检测并解压

### 3.2 前端渲染进程模块

#### 3.2.1 上传UI组件 (src/components/Upload/)

**设计思路**: 提供统一的上传界面，支持多种上传方式。

**核心组件**:
- **UploadDialog**: 上传对话框，集成文件选择和属性配置
- **FileUploadArea**: 文件上传区域，支持拖拽和点击上传
- **useTusUpload**: 上传逻辑组合函数，封装TUS上传功能

#### 3.2.2 下载UI组件

**设计思路**: 集成在文件列表中，提供便捷的下载功能。

**核心功能**:
- **useFileDownload**: 下载逻辑组合函数
- **路径选择**: 支持下载路径选择和记忆功能
- **批量下载**: 支持多文件批量下载

#### 3.2.3 全局状态管理 (src/store/)

**设计思路**: 使用Pinia进行状态管理，支持持久化存储。

**核心Store**:
- **sidebar**: 侧边栏状态管理
- **auth**: 认证状态管理

**数据流设计**:
```mermaid
graph LR
    A[用户操作] --> B[Composable]
    B --> C[Pinia Store]
    C --> D[持久化存储]
    C --> E[UI更新]
```

## 4. 关键设计决策

### 4.1 技术选型理由

- **Vue 3 Composition API**: 提供更好的逻辑复用和类型推导
- **TUS协议**: 标准化的断点续传协议，可靠性高
- **StreamSaver**: 避免大文件下载时的内存问题
- **7z压缩**: 高压缩比，支持多种格式
- **Pinia**: Vue 3官方推荐的状态管理库

### 4.2 架构设计考量

- **模块化设计**: 每个功能模块独立，便于维护和测试
- **IPC通信**: 主进程和渲染进程通过IPC安全通信
- **事件驱动**: 基于事件的异步处理机制
- **类型安全**: 完整的TypeScript类型支持

### 4.3 性能优化策略

- **智能打包**: 减少网络请求次数，优化传输效率
- **并发控制**: 合理控制上传下载并发数，避免资源竞争
- **流式处理**: 大文件流式处理，避免内存溢出
- **虚拟滚动**: 大量文件列表使用虚拟滚动优化性能

## 5. 开发和部署指南

### 5.1 环境搭建

```bash
# 1. 安装Node.js (推荐v18+)
# 2. 安装pnpm
npm install -g pnpm

# 3. 克隆项目并安装依赖
git clone <repository-url>
cd cloudDrive
pnpm install
```

### 5.2 常用开发命令

```bash
# 开发模式（前端+Electron）
pnpm dev:electron

# 仅前端开发
pnpm dev

# 类型检查
pnpm type-check

# 代码检查
pnpm lint

# 清理构建文件
pnpm clean
```

### 5.3 打包和发布

```bash
# 构建生产版本
pnpm build

# 打包macOS应用
pnpm build:mac

# 打包Windows应用
pnpm build:win

# 打包测试版本
pnpm build:test:mac
pnpm build:test:win
```

### 5.4 环境配置

项目支持多环境配置，通过环境变量文件管理：
- `.env.development`: 开发环境配置
- `.env.production`: 生产环境配置
- `.env.test`: 测试环境配置

关键环境变量：
- `VITE_TUS_ENDPOINT`: TUS上传服务端点
- `VITE_API_BASE_URL`: API基础URL
- `VITE_DOWNLOAD_ENDPOINT`: 下载服务端点
- `VITE_SMART_PACKING_THRESHOLD`: 智能打包阈值

## 6. 核心业务流程详解

### 6.1 文件上传完整流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant UI as 上传UI
    participant C as useTusUpload
    participant M as TusUploadManager
    participant A as ArchiveManager
    participant S as TUS服务器

    U->>UI: 选择文件/拖拽文件
    UI->>C: uploadFiles(files)
    C->>C: 检查文件数量

    alt 文件数量 >= 10
        C->>A: 触发智能打包
        A->>A: 7z压缩(级别0)
        A-->>C: 返回压缩文件路径
    end

    C->>M: createUploadTask(filePath)
    M->>M: 生成任务ID
    M->>S: 创建TUS上传会话
    S-->>M: 返回上传URL

    C->>M: startUpload(taskId)
    M->>S: 分片上传

    loop 上传进度
        S-->>M: 上传进度
        M-->>C: 进度回调
        C-->>UI: 更新进度条
    end

    S-->>M: 上传完成
    M-->>C: 完成回调
    C->>C: 调用业务回调
    C-->>UI: 刷新文件列表
```

### 6.2 文件下载完整流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant UI as 文件列表
    participant D as useFileDownload
    participant M as DownloadManager
    participant E as ExtractionManager
    participant S as 下载服务器

    U->>UI: 点击下载
    UI->>D: downloadItem(item)
    D->>D: 显示路径选择对话框
    U->>D: 选择下载路径

    D->>M: createDownloadTask(url, path)
    M->>S: 发起下载请求
    S-->>M: 开始流式传输

    loop 下载进度
        S-->>M: 数据流
        M->>M: 写入本地文件
        M-->>D: 进度回调
        D-->>UI: 更新进度条
    end

    S-->>M: 下载完成
    M->>M: 检查文件类型

    alt 是7z文件
        M->>E: 触发自动解压
        E->>E: 解压到同目录
        E-->>M: 解压完成
    end

    M-->>D: 下载完成回调
    D-->>UI: 显示完成通知
```

### 6.3 智能打包触发机制

```mermaid
flowchart TD
    A[用户选择文件] --> B{文件数量检查}
    B -->|< 10个文件| C[直接上传]
    B -->|≥ 10个文件| D[触发智能打包]

    D --> E[创建临时目录]
    E --> F[7z压缩处理]
    F --> G{压缩成功?}
    G -->|是| H[上传压缩文件]
    G -->|否| I[回退到直接上传]

    H --> J[上传完成后清理临时文件]
    C --> K[正常上传流程]
    I --> K

    J --> L[完成]
    K --> L
```

## 7. 关键代码示例

### 7.1 TUS上传核心逻辑

```typescript
// electron/tus/uploadManager.ts 核心方法
async createUploadTask(filePath: string, metadata?: Record<string, string>): Promise<string> {
  const stats = await fs.stat(filePath);
  const taskId = this.generateTaskId();

  const task: UploadTask = {
    id: taskId,
    filePath,
    fileName: path.basename(filePath),
    fileSize: stats.size,
    status: "pending",
    metadata: { ...this.config.metadata, ...metadata }
  };

  this.tasks.set(taskId, task);
  this.saveTaskToStore(task);
  this.emit("task-created", taskId, task);

  return taskId;
}
```

### 7.2 智能打包判断逻辑

```typescript
// src/components/Upload/composables/useTusUpload.ts
const shouldTriggerSmartPacking = (files: File[]): boolean => {
  const threshold = config.smartPacking.threshold; // 默认10
  return files.length >= threshold;
};

const uploadFiles = async (files: File[]) => {
  if (shouldTriggerSmartPacking(files)) {
    // 触发智能打包流程
    const archiveResult = await window.electronAPI.archive.createArchive(filePaths);
    if (archiveResult.success) {
      // 上传压缩文件
      await uploadSingleFile(archiveResult.archivePath);
    }
  } else {
    // 直接上传
    for (const file of files) {
      await uploadSingleFile(file);
    }
  }
};
```

### 7.3 流式下载实现

```typescript
// electron/stream-downloader/downloadManager.ts
async startDownload(taskId: string): Promise<void> {
  const task = this.tasks.get(taskId);
  if (!task) throw new Error(`任务不存在: ${taskId}`);

  const response = await axios({
    method: 'GET',
    url: task.url,
    responseType: 'stream',
    headers: task.resumeFrom ? { 'Range': `bytes=${task.resumeFrom}-` } : {}
  });

  const writeStream = fs.createWriteStream(task.savePath, {
    flags: task.resumeFrom ? 'a' : 'w'
  });

  response.data.pipe(writeStream);

  response.data.on('data', (chunk: Buffer) => {
    this.handleProgress(taskId, chunk.length);
  });

  writeStream.on('finish', () => {
    this.handleSuccess(taskId);
  });
}
```

## 8. 常见问题和解决方案

### 8.1 大文件上传内存问题
**问题**: 大文件上传时内存占用过高
**解决方案**:
- 优先使用文件路径而非File对象
- 启用TUS分片上传（默认20MB分片）
- 使用文件选择对话框避免内存拷贝

### 8.2 下载中断恢复
**问题**: 下载过程中网络中断
**解决方案**:
- 实现HTTP Range请求支持断点续传
- 本地保存下载进度状态
- 自动检测并恢复中断的下载

### 8.3 智能打包性能优化
**问题**: 大量小文件打包耗时过长
**解决方案**:
- 使用7z压缩级别0（仅存储，不压缩）
- 异步处理压缩任务，不阻塞UI
- 合理设置打包阈值（默认10个文件）

### 8.4 跨平台兼容性
**问题**: Windows和macOS路径处理差异
**解决方案**:
- 统一使用Node.js path模块处理路径
- 文件路径使用正斜杠分隔符
- 特殊字符转义处理

## 9. 性能监控和调试

### 9.1 日志系统
项目集成了完整的日志系统：
- **主进程日志**: 使用electron-log，自动保存到用户数据目录
- **渲染进程日志**: 使用浏览器控制台，开发时可见
- **模块化日志**: 每个模块有独立的logger实例

### 9.2 性能指标
关键性能指标监控：
- **上传速度**: 实时计算并显示上传速度
- **下载速度**: 流式下载速度监控
- **内存使用**: 大文件处理时的内存占用
- **CPU使用**: 压缩解压时的CPU占用

### 9.3 调试工具
- **开发者工具**: 开发模式下自动打开
- **IPC调试**: 主进程和渲染进程通信调试
- **网络调试**: HTTP请求和响应监控

## 10. 快速上手指南

### 10.1 新开发者入门步骤

1. **环境准备**
   ```bash
   # 安装Node.js 18+
   # 安装pnpm
   npm install -g pnpm

   # 克隆项目
   git clone <repository-url>
   cd cloudDrive

   # 安装依赖
   pnpm install
   ```

2. **开发环境配置**
   ```bash
   # 复制环境变量文件
   cp .env.development .env.local

   # 根据实际情况修改配置
   # 主要配置项：API地址、TUS端点、下载端点
   ```

3. **启动开发服务**
   ```bash
   # 启动完整开发环境（推荐）
   pnpm dev:electron

   # 或分别启动
   pnpm dev        # 前端开发服务器
   pnpm electron   # Electron主进程
   ```

4. **代码结构理解**
   - 先阅读 `electron/main.ts` 了解主进程初始化
   - 查看 `src/App.vue` 了解前端应用结构
   - 重点关注 `src/composables/` 目录的业务逻辑
   - 理解 `electron/tus/` 和 `electron/stream-downloader/` 核心模块

### 10.2 常见开发任务

#### 添加新的上传功能
1. 在 `src/components/Upload/composables/useTusUpload.ts` 中添加方法
2. 在 `electron/tus/uploadManager.ts` 中实现主进程逻辑
3. 通过 `electron/tus/ipcHandlers.ts` 注册IPC处理器
4. 在UI组件中调用新功能

#### 添加新的下载功能
1. 在 `src/composables/useFileDownload.ts` 中添加方法
2. 在 `electron/stream-downloader/downloadManager.ts` 中实现逻辑
3. 更新相关的类型定义

#### 修改UI组件
1. 优先查看 `src/components/ui/` 是否有合适的shadcn-vue组件
2. 在 `src/components/` 中创建或修改业务组件
3. 使用Tailwind CSS进行样式设计
4. 确保响应式设计和无障碍访问

### 10.3 调试技巧

#### 主进程调试
```bash
# 启动时会自动开启调试端口9229
pnpm electron
# 在Chrome中访问 chrome://inspect 进行调试
```

#### 渲染进程调试
- 开发模式下自动打开开发者工具
- 使用Vue DevTools浏览器扩展
- 查看Console面板的日志输出

#### IPC通信调试
```typescript
// 在主进程中添加日志
console.log('IPC调用:', event, args);

// 在渲染进程中添加日志
console.log('IPC响应:', result);
```
