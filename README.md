# 星图 (CloudDrive) 核心技术文档

## 1. 项目概述

星图是一款基于 Electron + Vue 3 + TypeScript 构建的现代化云盘桌面应用程序，专为企业级文件管理和高效传输而设计。

### 核心技术特性

- **TUS协议断点续传**: 基于TUS协议的可靠大文件上传解决方案
- **智能压缩打包**: 10个文件即触发7z快速压缩（压缩级别0），优化网络传输
- **流式下载**: 基于StreamSaver的大文件流式下载

## 2. 技术架构

### 2.1 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    渲染进程 (Vue 3 + TypeScript)             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │     文件管理界面     │     上传下载控制     │     进度监控    │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │ IPC Communication
┌─────────────────────────────────────────────────────────────┐
│                     主进程 (Electron)                       │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  TUS Upload  │  Stream Download  │  7z Archive/Extract │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │ File System & Network
┌─────────────────────────────────────────────────────────────┐
│                      操作系统 & 网络                        │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心技术栈
- **前端**: Vue 3 (Composition API) + TypeScript + Electron 28.0.0
- **文件传输**: tus-js-client (TUS协议) + StreamSaver (流式下载)
- **压缩处理**: node-7z + 7zip-bin
- **构建工具**: Vite + electron-builder

## 3. 项目结构

### 3.1 核心模块结构
```
cloudDrive/
├── electron/                 # Electron主进程核心模块
│   ├── tus/                 # TUS上传模块
│   │   ├── uploadManager.ts # 上传管理器
│   │   └── types.ts         # 类型定义
│   ├── stream-downloader/   # 流式下载模块
│   │   ├── downloadManager.ts # 下载管理器
│   │   └── types.ts         # 类型定义
│   ├── archive/             # 压缩模块
│   │   ├── archiveManager.ts # 压缩管理器
│   │   └── types.ts         # 类型定义
│   └── 7z-extractor/       # 解压缩模块
│       ├── extractionManager.ts # 解压管理器
│       └── worker.ts        # 后台工作进程
├── src/                     # Vue前端源码
│   ├── components/Upload/   # 上传组件
│   ├── composables/         # 组合式函数
│   │   ├── useFileDownload.ts
│   │   └── useTusUpload.ts
│   └── api/services/        # API服务层
└── dist-electron/           # Electron构建输出
```





## 4. 核心功能模块详解

### 4.1 文件上传模块 - TUS协议实现

#### 4.1.1 模块架构
文件上传模块基于 TUS (Tus Resumable Upload) 协议实现，提供可靠的断点续传上传功能。

```typescript
// 核心类结构
class TusUploadManager extends EventEmitter {
  private tasks: Map<string, UploadTask> = new Map();
  private config: TusUploadConfig;
  private archiveManager?: ArchiveManager;

  // 核心方法
  async createUploadTask(filePath: string, metadata?: Record<string, string>): Promise<string>
  async startUpload(taskId: string): Promise<void>
  async pauseUpload(taskId: string): Promise<void>
  async resumeUpload(taskId: string): Promise<void>
  async cancelUpload(taskId: string): Promise<void>
}
```

#### 4.1.2 TUS协议实现细节

**协议流程**:
1. **创建上传会话**: 向服务器发送 POST 请求创建上传会话
2. **获取上传URL**: 服务器返回专用的上传URL
3. **分片上传**: 将文件分割成固定大小的分片进行上传
4. **断点续传**: 上传中断后可从上次位置继续
5. **完成验证**: 所有分片上传完成后进行完整性验证

**关键配置**:
```typescript
interface TusUploadConfig {
  endpoint: string;              // TUS服务器端点
  chunkSize: number;            // 分片大小 (默认20MB)
  retryDelays: number[];        // 重试延迟策略 [0, 1000, 3000, 5000]
  parallelUploads: number;      // 并行上传数量 (默认5)
  metadata: Record<string, string>; // 默认元数据
  headers: Record<string, string>;  // 默认请求头
}
```

#### 4.1.3 智能打包逻辑

**触发条件**:
- 文件数量 ≥ 10 个文件时自动触发
- 支持用户手动选择是否打包
- 排除已压缩文件（.zip, .rar, .7z等）

**打包流程**:
```typescript
// 智能打包决策算法
class SmartPackingDecision {
  shouldPack(files: File[]): boolean {
    const fileCount = files.length;
    const threshold = config.smartPacking.threshold; // 默认10

    if (fileCount < threshold) return false;

    // 排除已压缩文件
    const compressedExtensions = ['.zip', '.rar', '.7z', '.tar', '.gz'];
    const uncompressedFiles = files.filter(file =>
      !compressedExtensions.some(ext => file.name.toLowerCase().endsWith(ext))
    );

    return uncompressedFiles.length >= threshold;
  }
}
```

**压缩策略**:
- 使用7z格式，压缩级别为0（最快压缩速度，优先用户体验）
- 保持原始目录结构
- 生成临时压缩文件，上传完成后自动清理

**配置优化说明**:
```typescript
// 智能打包配置优化 - 针对用户体验的平衡设计
const optimizedConfig = {
  // 阈值设为10：更早触发打包，减少HTTP请求数量
  threshold: 10,

  // 压缩级别0：最快压缩速度，减少用户等待时间
  compressionLevel: 0,
};
```

#### 4.1.4 拖拽上传实现

**拖拽处理流程**:
```typescript
// 拖拽事件处理
const useDragAndDrop = (options: DragDropOptions, callbacks: DragDropCallbacks) => {
  const handleDrop = async (event: DragEvent) => {
    event.preventDefault();

    // 1. 获取拖拽的文件和文件夹
    const items = Array.from(event.dataTransfer?.items || []);
    const files: File[] = [];

    // 2. 递归处理文件夹
    for (const item of items) {
      if (item.kind === 'file') {
        const entry = item.webkitGetAsEntry();
        if (entry) {
          await processEntry(entry, files);
        }
      }
    }

    // 3. 智能打包检查
    if (shouldTriggerSmartPack(files)) {
      callbacks.onSmartPackTriggered?.(files, files.length);
    } else {
      callbacks.onFilesProcessed?.({ files: files.map(f => ({ file: f })) });
    }
  };
};
```

#### 4.1.5 进度监控和状态管理

**任务状态定义**:
```typescript
type UploadStatus =
  | 'pending'     // 等待上传
  | 'uploading'   // 上传中
  | 'paused'      // 已暂停
  | 'completed'   // 已完成
  | 'failed'      // 上传失败
  | 'cancelled';  // 已取消
```

**进度计算**:
```typescript
interface UploadProgress {
  taskId: string;
  fileName: string;
  bytesUploaded: number;
  bytesTotal: number;
  percentage: number;
  speed: number;           // 上传速度 (bytes/s)
  remainingTime: number;   // 预估剩余时间 (seconds)
  status: UploadStatus;
}
```

**事件系统**:
```typescript
// 上传管理器事件
uploadManager.on('task-created', (taskId: string, task: UploadTask) => {});
uploadManager.on('task-progress', (taskId: string, progress: UploadProgress) => {});
uploadManager.on('task-completed', (taskId: string) => {});
uploadManager.on('task-failed', (taskId: string, error: Error) => {});
```

### 4.2 文件下载模块 - 流式下载系统

#### 4.2.1 流式下载原理

**StreamSaver技术**:
- 利用 Service Worker 拦截网络请求
- 通过 ReadableStream 实现流式数据处理
- 突破浏览器内存限制，支持任意大小文件下载

**核心实现**:
```typescript
class StreamDownloadManager extends EventEmitter {
  private tasks: Map<string, DownloadTask> = new Map();
  private concurrentLimit: number = 10;
  private activeDownloads: Set<string> = new Set();

  async startDownload(task: DownloadTask): Promise<void> {
    // 1. 检查并发限制
    if (this.activeDownloads.size >= this.concurrentLimit) {
      await this.waitForSlot();
    }

    // 2. 创建流式下载
    const response = await fetch(task.downloadUrl, {
      headers: this.buildHeaders(task)
    });

    // 3. 创建可写流
    const fileStream = streamSaver.createWriteStream(task.fileName, {
      size: task.fileSize
    });

    // 4. 管道连接，开始下载
    const readableStream = new ReadableStream({
      start(controller) {
        const reader = response.body?.getReader();
        return pump();

        function pump(): Promise<void> {
          return reader!.read().then(({ done, value }) => {
            if (done) {
              controller.close();
              return;
            }

            // 更新进度
            task.bytesDownloaded += value.byteLength;
            this.emitProgress(task);

            controller.enqueue(value);
            return pump();
          });
        }
      }
    });

    readableStream.pipeTo(fileStream);
  }
}
```

#### 4.2.2 断点续传实现

**Range请求支持**:
```typescript
// 构建Range请求头
private buildHeaders(task: DownloadTask): Record<string, string> {
  const headers: Record<string, string> = {
    'Authorization': `Bearer ${this.getAuthToken()}`
  };

  // 断点续传：添加Range头
  if (task.bytesDownloaded > 0) {
    headers['Range'] = `bytes=${task.bytesDownloaded}-`;
  }

  return headers;
}

// 恢复下载
async resumeDownload(taskId: string): Promise<void> {
  const task = this.tasks.get(taskId);
  if (!task) throw new Error('Task not found');

  // 检查本地文件大小
  const localFileSize = await this.getLocalFileSize(task.savePath);
  task.bytesDownloaded = localFileSize;

  // 重新开始下载
  await this.startDownload(task);
}
```

#### 4.2.3 批量下载管理

**队列管理**:
```typescript
class DownloadQueue {
  private queue: DownloadTask[] = [];
  private processing: boolean = false;

  async addTasks(tasks: DownloadTask[]): Promise<void> {
    this.queue.push(...tasks);
    if (!this.processing) {
      await this.processQueue();
    }
  }

  private async processQueue(): Promise<void> {
    this.processing = true;

    while (this.queue.length > 0) {
      const batch = this.queue.splice(0, this.concurrentLimit);

      // 并发下载
      await Promise.allSettled(
        batch.map(task => this.downloadManager.startDownload(task))
      );
    }

    this.processing = false;
  }
}
```

#### 4.2.4 自动解压缩集成

**下载完成后处理**:
```typescript
// 下载完成事件处理
downloadManager.on('task-completed', async (taskId: string) => {
  const task = this.tasks.get(taskId);
  if (!task) return;

  // 检查是否需要解压缩
  if (this.shouldExtract(task.fileName)) {
    const extractionTask = await this.extractionManager.createExtractionTask({
      archivePath: task.savePath,
      extractPath: path.dirname(task.savePath),
      deleteAfterExtraction: true
    });

    await this.extractionManager.startExtraction(extractionTask.id);
  }
});

private shouldExtract(fileName: string): boolean {
  const extractableExtensions = ['.7z', '.zip', '.rar'];
  return extractableExtensions.some(ext =>
    fileName.toLowerCase().endsWith(ext)
  );
}
```

### 4.3 压缩解压模块 - 7z处理系统

#### 4.3.1 7z压缩算法选择

**压缩配置**:
```typescript
interface CompressionConfig {
  method: '7z' | 'zip';           // 压缩格式
  level: 0 | 1 | 3 | 5 | 7 | 9;   // 压缩级别
  threads: number;                // 线程数
  dictionary: string;             // 字典大小
  wordSize: number;               // 词汇大小
}

// 默认配置 - 优先压缩速度
const defaultConfig: CompressionConfig = {
  method: '7z',
  level: 0,        // 最快压缩速度，减少等待时间
  threads: Math.min(4, os.cpus().length), // 最多4线程
  dictionary: '32m', // 32MB字典
  wordSize: 32
};
```

**压缩命令构建**:
```typescript
private buildCompressionCommand(
  inputPaths: string[],
  outputPath: string,
  config: CompressionConfig
): string[] {
  return [
    '7z',
    'a',                          // 添加到压缩包
    '-t7z',                       // 7z格式
    `-mx=${config.level}`,        // 压缩级别
    `-mmt=${config.threads}`,     // 线程数
    `-md=${config.dictionary}`,   // 字典大小
    `-mfb=${config.wordSize}`,    // 词汇大小
    '-y',                         // 自动确认
    outputPath,
    ...inputPaths
  ];
}
```

#### 4.3.2 自动压缩触发条件

**智能压缩决策**:
```typescript
class AutoCompressionDecision {
  shouldCompress(files: FileInfo[]): boolean {
    // 1. 文件数量检查
    if (files.length < 10) return false;

    // 2. 总大小检查 - 避免压缩已经很大的单个文件
    const totalSize = files.reduce((sum, file) => sum + file.size, 0);
    const averageSize = totalSize / files.length;

    // 3. 文件类型检查 - 排除已压缩文件
    const compressedTypes = ['.zip', '.rar', '.7z', '.tar.gz', '.bz2'];
    const compressibleFiles = files.filter(file =>
      !compressedTypes.some(type => file.name.toLowerCase().endsWith(type))
    );

    return compressibleFiles.length >= 10;
  }

  estimateCompressionRatio(files: FileInfo[]): number {
    // 根据文件类型估算压缩比
    let totalSize = 0;
    let estimatedCompressedSize = 0;

    for (const file of files) {
      totalSize += file.size;

      // 根据文件扩展名估算压缩比
      const ratio = this.getCompressionRatio(file.extension);
      estimatedCompressedSize += file.size * ratio;
    }

    return estimatedCompressedSize / totalSize;
  }

  private getCompressionRatio(extension: string): number {
    const ratios: Record<string, number> = {
      '.txt': 0.3,    // 文本文件压缩比很高
      '.log': 0.3,
      '.js': 0.4,     // 代码文件
      '.css': 0.4,
      '.html': 0.4,
      '.json': 0.4,
      '.jpg': 0.95,   // 图片文件已压缩
      '.png': 0.9,
      '.mp4': 0.98,   // 视频文件已压缩
      '.mp3': 0.98,
      '.pdf': 0.8,    // PDF有一定压缩空间
      '.docx': 0.7,   // Office文档
      '.xlsx': 0.7,
      '.pptx': 0.7,
    };

    return ratios[extension.toLowerCase()] || 0.6; // 默认压缩比
  }
}
```

#### 4.3.3 解压缩工作流程

**后台工作进程**:
```typescript
// worker.ts - 解压缩工作进程
import { Worker } from 'worker_threads';
import { spawn } from 'child_process';

class ExtractionWorker {
  private worker: Worker;

  constructor() {
    this.worker = new Worker(__filename, {
      workerData: { isWorker: true }
    });
  }

  async extract(task: ExtractionTask): Promise<void> {
    return new Promise((resolve, reject) => {
      this.worker.postMessage({
        type: 'extract',
        task
      });

      this.worker.on('message', (message) => {
        switch (message.type) {
          case 'progress':
            this.emitProgress(task.id, message.progress);
            break;
          case 'completed':
            resolve();
            break;
          case 'error':
            reject(new Error(message.error));
            break;
        }
      });
    });
  }
}

// 工作进程中的解压缩逻辑
if (workerData?.isWorker) {
  parentPort?.on('message', async (message) => {
    if (message.type === 'extract') {
      const task = message.task as ExtractionTask;

      try {
        const process = spawn('7z', [
          'x',                    // 解压缩
          task.archivePath,       // 源文件
          `-o${task.extractPath}`, // 输出目录
          '-y'                    // 自动确认
        ]);

        // 监听进度输出
        process.stdout.on('data', (data) => {
          const output = data.toString();
          const progress = parseProgress(output);
          if (progress) {
            parentPort?.postMessage({
              type: 'progress',
              progress
            });
          }
        });

        process.on('close', (code) => {
          if (code === 0) {
            parentPort?.postMessage({ type: 'completed' });
          } else {
            parentPort?.postMessage({
              type: 'error',
              error: `Extraction failed with code ${code}`
            });
          }
        });

      } catch (error) {
        parentPort?.postMessage({
          type: 'error',
          error: error.message
        });
      }
    }
  });
}
```

**进度解析**:
```typescript
function parseProgress(output: string): number | null {
  // 解析7z输出中的进度信息
  const progressMatch = output.match(/(\d+)%/);
  if (progressMatch) {
    return parseInt(progressMatch[1]);
  }

  // 解析文件数量进度
  const fileMatch = output.match(/(\d+) files, (\d+) folders/);
  if (fileMatch) {
    // 基于文件数量估算进度
    return null; // 需要更复杂的逻辑
  }

  return null;
}
```

### 4.4 内存优化和性能策略

#### 4.4.1 大文件处理的内存管理

**流式处理策略**:
```typescript
class MemoryOptimizedUploader {
  private readonly CHUNK_SIZE = 20 * 1024 * 1024; // 20MB分片
  private readonly MAX_CONCURRENT_CHUNKS = 3;     // 最大并发分片数

  async uploadLargeFile(filePath: string): Promise<void> {
    const fileSize = await this.getFileSize(filePath);
    const totalChunks = Math.ceil(fileSize / this.CHUNK_SIZE);

    // 使用流式读取，避免将整个文件加载到内存
    const fileStream = fs.createReadStream(filePath, {
      highWaterMark: this.CHUNK_SIZE
    });

    let chunkIndex = 0;
    const activeUploads = new Set<Promise<void>>();

    for await (const chunk of fileStream) {
      // 控制并发数量
      if (activeUploads.size >= this.MAX_CONCURRENT_CHUNKS) {
        await Promise.race(activeUploads);
      }

      const uploadPromise = this.uploadChunk(chunk, chunkIndex++, totalChunks)
        .finally(() => activeUploads.delete(uploadPromise));

      activeUploads.add(uploadPromise);
    }

    // 等待所有分片上传完成
    await Promise.all(activeUploads);
  }

  private async uploadChunk(
    chunk: Buffer,
    index: number,
    total: number
  ): Promise<void> {
    // 上传完成后立即释放内存
    try {
      await this.tusClient.uploadChunk(chunk, index);
    } finally {
      // 显式释放chunk内存
      chunk.fill(0);
    }
  }
}
```

#### 4.4.2 智能队列管理器

```typescript
class SmartQueueManager<T> {
  private queue: T[] = [];
  private processing = false;
  private concurrentLimit: number;
  private activeJobs = new Set<Promise<void>>();

  constructor(concurrentLimit: number = 5) {
    this.concurrentLimit = concurrentLimit;
  }

  async add(item: T, processor: (item: T) => Promise<void>): Promise<void> {
    return new Promise((resolve, reject) => {
      this.queue.push({
        item,
        processor,
        resolve,
        reject
      } as any);

      this.processQueue();
    });
  }

  private async processQueue(): Promise<void> {
    if (this.processing) return;
    this.processing = true;

    while (this.queue.length > 0 && this.activeJobs.size < this.concurrentLimit) {
      const job = this.queue.shift()!;
      const jobPromise = this.executeJob(job);

      this.activeJobs.add(jobPromise);
      jobPromise.finally(() => this.activeJobs.delete(jobPromise));
    }

    if (this.activeJobs.size === 0) {
      this.processing = false;
    } else {
      // 等待至少一个任务完成后继续处理队列
      await Promise.race(this.activeJobs);
      this.processing = false;
      this.processQueue();
    }
  }
}
```





## 7. 总结

### 7.1 技术亮点

1. **TUS协议断点续传**: 可靠的大文件上传解决方案
2. **流式下载**: 突破内存限制的大文件下载
3. **智能压缩**: 自动检测和7z快速压缩（10个文件阈值，压缩级别0）
4. **模块化架构**: 清晰的代码组织和职责分离
5. **性能优化**: 内存管理、并发控制、用户体验优化

### 7.2 配置优化策略

项目采用了针对用户体验优化的配置策略：

- **智能打包阈值**: 10个文件即触发，更早优化网络请求
- **压缩级别**: 0级压缩（最快速度），减少用户等待时间
- **设计理念**: 在大量小文件场景下，减少HTTP请求数量带来的性能提升远大于高压缩率的传输优化

### 7.3 开发建议

- 遵循TypeScript类型安全原则
- 使用组合式API进行逻辑复用
- 重视性能优化和用户体验
- 保持代码的可测试性和可维护性
- 及时更新依赖和安全补丁



---

## 结语

星图项目通过TUS协议断点续传、流式下载、智能7z压缩等核心技术，实现了高性能的文件传输系统。项目采用模块化架构设计，针对大量小文件场景进行了深度优化，在用户体验和传输效率之间取得了良好平衡。

*本文档专注于核心功能模块的技术实现细节。*
