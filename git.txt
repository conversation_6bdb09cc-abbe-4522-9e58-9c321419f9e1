> git checkout -q main
error: The following untracked working tree files would be overwritten by checkout:
	src/components/preview/FilePreviewModal.vue
	src/components/preview/ImagePreview.vue
	src/components/preview/VideoPreview.vue
	src/components/preview/composables/useFilePreview.ts
	src/components/preview/index.ts
	src/components/upload/FileAttributeSelector.vue
	src/components/upload/FileItem.vue
	src/components/upload/FilePreview.vue
	src/components/upload/FileUploadArea.vue
	src/components/upload/FolderItem.vue
	src/components/upload/UploadDialog.vue
	src/components/upload/composables/useFilePreview.ts
	src/components/upload/composables/useFileUpload.ts
	src/components/upload/composables/useTusUpload.ts
	src/components/upload/composables/useUploadStrategy.ts
	src/components/upload/index.ts
	src/views/folders/DetailPanel/BasicInfo.vue
	src/views/folders/DetailPanel/CustomProperties.vue
	src/views/folders/DetailPanel/EditableCustomProperties.vue
	src/views/folders/DetailPanel/FileProperties.vue
	src/views/folders/DetailPanel/FolderProperties.vue
	src/views/folders/DetailPanel/index.vue
	src/views/folders/DetailPanel/useCustomProperties.ts
	src/views/folders/FilesView/ActionButtonGroup.vue
	src/views/folders/FilesView/ContextMenu.vue
	src/views/folders/FilesView/FileItem.vue
	src/views/folders/FilesView/FolderItem.vue
	src/views/folders/FilesView/GridView.vue
	src/views/folders/FilesView/ListView.vue
	src/views/folders/FilesView/Pagination.vue
	src/views/folders/FilesView/RenameInput.vue
	src/views/folders/FilesView/index.vue
	src/views/folders/FilesView/useRename.ts
	src/views/folders/FilterList.vue
	src/views/folders/FolderView.vue
	src/views/folders/ToolsBar/index.vue
Please move or remove them before you switch branches.
Aborting
