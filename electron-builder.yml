appId: com.clouddrive.app
productName: 星图

# 注入环境信息到 package.json
extraMetadata:
  buildEnv: "production"
  isTestBuild: false

directories:
  output: release

# 应用程序图标配置
icon: logo.png

extraResources:
  - from: .env.development
    to: .env.development
  - from: .env.production
    to: .env.production
  - from: .env.test
    to: .env.test

files:
  - dist/**/*
  - dist-electron/**/*

# Windows配置
win:
  icon: logo.png
  target:
    target: nsis
    arch:
      - x64

# macOS配置
mac:
  icon: logo.png
  target: dmg

# NSIS安装程序配置
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true

# 发布配置 - 用于自动更新
publish:
  provider: generic
  url: "http://172.20.40.47/client_packages/update-files/"