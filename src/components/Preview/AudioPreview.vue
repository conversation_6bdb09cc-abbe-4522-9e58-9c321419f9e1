<template>
  <div class="relative w-full h-[90vh] flex items-center justify-center bg-gradient-to-br from-background to-muted/20">
    <!-- 加载状态 -->
    <div v-if="isLoading"
      class="flex absolute inset-0 z-10 justify-center items-center backdrop-blur-sm bg-background/80">
      <div class="flex flex-col gap-2 items-center">
        <div class="w-8 h-8 rounded-full border-b-2 animate-spin border-primary"></div>
        <p class="text-sm text-muted-foreground">加载中...</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="flex flex-col gap-4 items-center p-8 text-center">
      <div class="flex justify-center items-center w-16 h-16 rounded-full bg-destructive/10">
        <svg class="w-8 h-8 text-destructive" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <div>
        <h3 class="mb-1 font-medium text-foreground">音频加载失败</h3>
        <p class="text-sm text-muted-foreground">{{ error }}</p>
      </div>
      <button @click="retryLoad"
        class="px-4 py-2 rounded-md transition-colors bg-primary text-primary-foreground hover:bg-primary/90">
        重试
      </button>
    </div>

    <!-- 音频预览 -->
    <div v-else class="flex flex-col items-center justify-center w-full max-w-2xl px-8">
      <!-- 音频可视化区域 -->
      <div class="flex flex-col items-center mb-8">
        <!-- 音频图标 -->
        <div
          class="flex justify-center items-center w-32 h-32 mb-6 rounded-full bg-primary/10 border-4 border-primary/20">
          <svg class="w-16 h-16 text-primary" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z" />
          </svg>
        </div>

        <!-- 文件名 -->
        <h3 class="text-lg font-medium text-center text-foreground mb-2">
          {{ fileName || '音频文件' }}
        </h3>

        <!-- 播放状态指示 -->
        <div class="flex items-center gap-2 text-sm text-muted-foreground">
          <div v-if="audioPlayer.isPlaying.value" class="flex items-center gap-1">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>正在播放</span>
          </div>
          <div v-else-if="audioPlayer.isPaused.value" class="flex items-center gap-1">
            <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
            <span>已暂停</span>
          </div>
          <div v-else class="flex items-center gap-1">
            <div class="w-2 h-2 bg-muted-foreground rounded-full"></div>
            <span>准备就绪</span>
          </div>
        </div>
      </div>

      <!-- 音频控制面板 -->
      <div class="w-full max-w-lg">
        <!-- 进度条区域 -->
        <div class="mb-6">
          <!-- 时间显示 -->
          <div class="flex justify-between items-center mb-2 text-sm text-muted-foreground">
            <span>{{ formatTime(audioPlayer.currentTime.value) }}</span>
            <span>{{ formatTime(audioPlayer.duration.value) }}</span>
          </div>

          <!-- 进度条 -->
          <div class="relative">
            <!-- 背景轨道 -->
            <div class="w-full h-2 rounded-full bg-muted cursor-pointer" @click="handleProgressClick">
              <!-- 缓冲进度 -->
              <div class="absolute top-0 left-0 h-full rounded-full bg-muted-foreground/30 transition-all duration-300"
                :style="{ width: `${audioPlayer.buffered.value}%` }"></div>
              <!-- 播放进度 -->
              <div class="absolute top-0 left-0 h-full rounded-full bg-primary transition-all duration-100"
                :style="{ width: `${audioPlayer.progress.value}%` }"></div>
              <!-- 进度指示器 -->
              <div
                class="absolute top-1/2 w-4 h-4 bg-primary rounded-full shadow-lg transform -translate-y-1/2 transition-all duration-100"
                :style="{ left: `calc(${audioPlayer.progress.value}% - 8px)` }"></div>
            </div>
          </div>
        </div>

        <!-- 控制按钮区域 -->
        <div class="flex justify-center items-center gap-4 mb-6">
          <!-- 快退按钮 -->
          <button @click="audioPlayer.skipBackward(10)" class="p-2 rounded-full hover:bg-accent transition-colors"
            :disabled="!audioPlayer.canPlay.value">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M11 18V6l-8.5 6 8.5 6zm.5-6l8.5 6V6l-8.5 6z" />
            </svg>
          </button>

          <!-- 播放/暂停按钮 -->
          <button @click="audioPlayer.togglePlay()"
            class="p-4 rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition-colors shadow-lg"
            :disabled="!audioPlayer.canPlay.value">
            <svg v-if="!audioPlayer.isPlaying.value" class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8 5v14l11-7z" />
            </svg>
            <svg v-else class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z" />
            </svg>
          </button>

          <!-- 快进按钮 -->
          <button @click="audioPlayer.skipForward(10)" class="p-2 rounded-full hover:bg-accent transition-colors"
            :disabled="!audioPlayer.canPlay.value">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M4 18l8.5-6L4 6v12zm9-12v12l8.5-6L13 6z" />
            </svg>
          </button>
        </div>

        <!-- 音量控制 -->
        <div class="flex justify-center items-center gap-3">
          <!-- 静音按钮 -->
          <button @click="audioPlayer.toggleMute()" class="p-1 rounded hover:bg-accent transition-colors">
            <svg v-if="!audioPlayer.isMuted.value && audioPlayer.volume.value > 0.5" class="w-5 h-5" fill="currentColor"
              viewBox="0 0 24 24">
              <path
                d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z" />
            </svg>
            <svg v-else-if="!audioPlayer.isMuted.value && audioPlayer.volume.value > 0" class="w-5 h-5"
              fill="currentColor" viewBox="0 0 24 24">
              <path d="M18.5 12c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM5 9v6h4l5 5V4L9 9H5z" />
            </svg>
            <svg v-else class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path
                d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z" />
            </svg>
          </button>

          <!-- 音量滑块 -->
          <div class="flex items-center gap-2 w-24">
            <div class="relative w-full h-1 rounded-full bg-muted cursor-pointer" @click="handleVolumeClick">
              <div class="absolute top-0 left-0 h-full rounded-full bg-primary transition-all duration-100"
                :style="{ width: `${audioPlayer.volume.value * 100}%` }"></div>
              <div
                class="absolute top-1/2 w-3 h-3 bg-primary rounded-full shadow transform -translate-y-1/2 transition-all duration-100"
                :style="{ left: `calc(${audioPlayer.volume.value * 100}% - 6px)` }"></div>
            </div>
          </div>

          <!-- 音量百分比 -->
          <span class="text-xs text-muted-foreground min-w-[2rem]">
            {{ Math.round(audioPlayer.volume.value * 100) }}%
          </span>
        </div>
      </div>

      <!-- 隐藏的音频元素 -->
      <audio ref="audioElement" :src="audioUrl" preload="auto" autoplay style="display: none;"
        @canplay="handleCanPlay"></audio>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { useAudioPlayer, formatTime } from './composables/useAudioPlayer'

interface Props {
  audioUrl: string
  fileName?: string
  isLoading?: boolean
  error?: string | null
}

interface Emits {
  (e: 'retry'): void
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
  error: null
})

const emit = defineEmits<Emits>()

// 音频元素引用
const audioElement = ref<HTMLAudioElement>()

// 使用音频播放器
const audioPlayer = useAudioPlayer({
  audioUrl: props.audioUrl,
  autoPlay: true,
  volume: 0.8
})

// 监听URL变化
watch(() => props.audioUrl, () => {
  if (audioElement.value) {
    audioPlayer.audioRef.value = audioElement.value;
    audioPlayer.setupAudioEvents();
  }
}, { immediate: true })

// 组件挂载后设置音频元素
onMounted(() => {
  if (audioElement.value) {
    audioPlayer.audioRef.value = audioElement.value;
    audioPlayer.setupAudioEvents();
  }
})

/**
 * 处理音频可以播放事件
 */
function handleCanPlay() {
  // 音频可以播放时的处理
}

/**
 * 处理进度条点击
 */
function handleProgressClick(event: MouseEvent) {
  if (!audioPlayer.canPlay.value) return;

  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
  const clickX = event.clientX - rect.left;
  const percentage = clickX / rect.width;
  const newTime = percentage * audioPlayer.duration.value;

  audioPlayer.seek(newTime);
}

/**
 * 处理音量条点击
 */
function handleVolumeClick(event: MouseEvent) {
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  const clickX = event.clientX - rect.left
  const percentage = Math.max(0, Math.min(1, clickX / rect.width))

  audioPlayer.setVolume(percentage)
}

/**
 * 重试加载
 */
function retryLoad() {
  audioPlayer.clearError()
  emit('retry')
}
</script>
