<template>
  <nav class="w-full border-b shadow-sm bg-background border-border">
    <div class="px-6">
      <div class="flex justify-between items-center h-16">
        <!-- 左侧导航项 -->
        <div class="flex items-center space-x-8">

          <!-- 导航菜单 -->
          <div class="hidden md:block">
            <div class="flex items-baseline space-x-2">
              <Button v-for="item in navigationItems" :key="item.name" :variant="item.current ? 'default' : 'ghost'"
                @click="handleNavigation(item)" class="transition-colors duration-200">
                <component :is="item.icon" class="mr-2 w-4 h-4" />
                {{ item.name }}
              </Button>
            </div>
          </div>
        </div>

        <!-- 右侧用户头像和下拉菜单 -->
        <div class="relative">
          <div>
            <Button variant="ghost" size="icon" @click="toggleDropdown" class="rounded-full">
              <div
                class="flex justify-center items-center w-full h-full text-xs font-medium text-white bg-gradient-to-br from-blue-500 to-purple-600 rounded-full">
                {{ userInitials }}
              </div>
            </Button>
          </div>

          <!-- 下拉菜单 -->
          <div v-show="isDropdownOpen"
            class="absolute right-0 z-50 mt-2 w-48 rounded-md border shadow-lg origin-top-right bg-popover border-border focus:outline-none"
            role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button">
            <div class="py-1" role="none">
              <!-- 用户信息 -->
              <div class="px-4 py-2 border-b border-border">
                <p class="text-sm font-medium text-popover-foreground">{{ user.name }}</p>
                <p class="text-sm text-muted-foreground">{{ user.email }}</p>
              </div>

              <!-- 菜单项 -->
              <Button v-for="item in userMenuItems" :key="item.name" variant="ghost" size="sm"
                @click="handleUserMenuClick(item)"
                class="justify-start px-4 py-2 w-full text-sm rounded-none hover:bg-accent">
                <component :is="item.icon" class="mr-3 w-4 h-4" />
                {{ item.name }}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>

  </nav>
</template>

<script setup lang="ts">
import { ref, computed, inject, onMounted, onUnmounted } from 'vue'
import { LogOut, Database, Tags, Shield, RefreshCw, HelpCircle } from 'lucide-vue-next'
import Button from '@/components/ui/button/Button.vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuth } from '@/composables/useAuth'
import { useUserStore } from '@/store/user'
import type { NavigationItem } from '@/types/user'

interface UserMenuItem {
  name: string
  icon: any
  action: string
}

const router = useRouter()
const route = useRoute()
const { logout } = useAuth()
const userStore = useUserStore()

// 注入检查更新方法
const checkForUpdates = inject<(() => void) | null>('checkForUpdates', null)

const isDropdownOpen = ref(false)

// 菜单代码到图标的映射
const menuIconMap = {
  'resources': Database,
  'tags': Tags,
  'permissions': Shield
}

// 菜单代码到路由的映射
const menuRouteMap = {
  'resources': '/resources',
  'tags': '/tags',
  'permissions': '/permissions'
}

// 根据API菜单数据和当前路由动态计算导航项，过滤掉回收站
const navigationItems = computed(() => {
  return userStore.menu
    .filter(menuItem => menuItem.code !== 'recycle') // 过滤掉回收站，只在侧边栏显示
    .map(menuItem => ({
      ...menuItem,
      href: menuRouteMap[menuItem.code as keyof typeof menuRouteMap] || `/${menuItem.code}`,
      icon: menuIconMap[menuItem.code as keyof typeof menuIconMap] || Database,
      current: isRouteActive(menuRouteMap[menuItem.code as keyof typeof menuRouteMap] || `/${menuItem.code}`)
    }))
})

// 判断路由是否激活
const isRouteActive = (href: string) => {
  const currentPath = route.path

  // 精确匹配或路径前缀匹配
  if (href === '/resources') {
    // 资源库页面包括子路由
    return currentPath === '/resources' || currentPath.startsWith('/resources/')
  } else {
    // 其他页面精确匹配
    return currentPath === href
  }
}

// 用户信息（从store获取）
const user = computed(() => ({
  name: userStore.displayName,
  email: userStore.userInfo?.user_id || '<EMAIL>', // 使用user_id作为邮箱显示
}))

// 用户头像显示的首字母
const userInitials = computed(() => {
  const name = userStore.displayName
  if (name.length === 0) return '用'
  // 如果是中文名，取第一个字符
  if (/[\u4e00-\u9fa5]/.test(name)) {
    return name.charAt(0)
  }
  // 如果是英文名，取首字母
  return name.charAt(0).toUpperCase()
})

// 用户菜单项
const userMenuItems = ref<UserMenuItem[]>([
  { name: '帮助手册', icon: HelpCircle, action: 'help' },
  { name: '检查更新', icon: RefreshCw, action: 'checkUpdate' },
  { name: '退出登录', icon: LogOut, action: 'logout' }
])

const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value
}

const handleNavigation = (item: NavigationItem) => {
  // 直接跳转，激活状态由路由变化自动更新
  router.push(item.href)
}

const handleUserMenuClick = (item: UserMenuItem) => {
  switch (item.action) {
    case 'help':
      handleHelp()
      break
    case 'checkUpdate':
      handleCheckUpdate()
      break
    case 'settings':
      console.log('打开个人设置')
      // 这里添加打开设置页面的逻辑
      break
    case 'logout':
      handleLogout()
      break
    default:
      console.log('未知操作:', item.action)
  }

  // 关闭下拉菜单
  isDropdownOpen.value = false
}

const handleHelp = () => {
  console.log('打开帮助手册...')
  // 打开外部链接
  window.open('https://doc.weixin.qq.com/doc/w3_Ad0AhQZ5AIkRFK0DFYrRpmyE07Ur6?scode=ALkAtweOACEOhNz2T0Ad0AhQZ5AIk', '_blank')
}

const handleCheckUpdate = () => {
  if (checkForUpdates) {
    console.log('执行检查更新...')
    checkForUpdates()
  } else {
    console.warn('检查更新功能未可用')
  }
}

const handleLogout = async () => {
  // 确认对话框
  if (confirm('确定要退出登录吗？')) {
    console.log('执行退出登录...')

    try {
      // 使用认证系统的退出登录功能
      logout()

      // 等待一小段时间确保退出登录流程完成
      await new Promise(resolve => setTimeout(resolve, 100))

      // 跳转到登录页面
      router.push('/login')

      console.log('✅ 退出登录成功')
    } catch (error) {
      console.error('❌ 退出登录失败:', error)
      // 即使出错也要跳转到登录页面
      try {
        router.push('/login')
      } catch (routerError) {
        console.error('❌ 路由跳转失败:', routerError)
        // 发送强制导航事件而不是使用window.location.href
        window.dispatchEvent(
          new CustomEvent('force-navigate-to-login', {
            detail: { reason: 'logout-router-failed' }
          })
        )
      }
    }
  }
}

// 点击外部关闭下拉菜单
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    isDropdownOpen.value = false
  }
}

// 生命周期钩子
onMounted(async () => {
  document.addEventListener('click', handleClickOutside)

  // 确保用户信息已加载（添加isLoading检查，避免重复调用）
  try {
    if (!userStore.isLoaded && !userStore.isLoading && !userStore.error) {
      await userStore.fetchUserInfo(false)
    }
  } catch (error) {
    console.warn('Navigation组件: 用户信息加载失败:', error)
    // 错误已在store中严格处理，用户将被重定向到登录页面
    // 如果已经有错误状态，不再重复尝试
  }
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
/* 自定义样式 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>