<template>
    <div>
        <!-- 更新通知对话框 -->
        <UpdateNotificationDialog v-model:open="showUpdateNotification" :version-info="availableVersion"
            :current-version="currentVersion" @update-now="handleStartDownload" @update-later="handleUpdateLater"
            @close="handleNotificationClose" />

        <!-- 下载进度对话框 -->
        <DownloadProgressDialog v-model:open="showDownloadProgress" :progress="downloadProgress"
            :is-downloading="isDownloading" :error="downloadError" :show-retry-button="!!downloadError"
            :show-cancel-button="!!downloadError" :show-close-button="false" @retry="handleRetryDownload"
            @cancel="handleCancelDownload" @close="handleDownloadClose" />

        <!-- 安装确认对话框 -->
        <InstallConfirmDialog v-model:open="showInstallConfirm" :version-info="availableVersion"
            :is-installing="isInstalling" @install-now="handleStartInstall" @install-later="handleInstallLater"
            @close="handleInstallClose" />

        <!-- 无更新可用对话框 -->
        <NoUpdateDialog v-model:open="showNoUpdate" :current-version="currentVersion" :message="noUpdateMessage"
            @close="handleNoUpdateClose" />
    </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { toast } from 'vue-sonner'
import UpdateNotificationDialog from './UpdateNotificationDialog.vue'
import DownloadProgressDialog from './DownloadProgressDialog.vue'
import InstallConfirmDialog from './InstallConfirmDialog.vue'
import NoUpdateDialog from './NoUpdateDialog.vue'
import type { VersionInfo, UpdateCheckResult, DownloadProgress } from '../../types/electron'

interface Props {
    autoCheckOnMount?: boolean
}

interface Emits {
    'update-available': [version: VersionInfo]
    'update-downloaded': [version: VersionInfo]
    'update-installed': []
    'update-error': [error: string]
}

const props = withDefaults(defineProps<Props>(), {
    autoCheckOnMount: true  // 改为 true，确保每次挂载时都检查更新
})

const emit = defineEmits<Emits>()

// 状态管理
const currentVersion = ref<string>('1.0.0')
const availableVersion = ref<VersionInfo | undefined>()
const downloadProgress = ref<DownloadProgress>({
    percent: 0,
    bytesDownloaded: 0,
    totalBytes: 0,
    speed: 0
})

// 对话框显示状态
const showUpdateNotification = ref(false)
const showDownloadProgress = ref(false)
const showInstallConfirm = ref(false)
const showNoUpdate = ref(false)

// 操作状态
const isDownloading = ref(false)
const isInstalling = ref(false)

const downloadError = ref<string>('')
const noUpdateMessage = ref('已是最新版本')

// 获取 Electron API（安全检查）
const electronAPI = typeof window !== 'undefined' ? (window as any).electronAPI : null

// 监听对话框状态变化
watch(showUpdateNotification, (newVal, oldVal) => {
    console.log('🔔 showUpdateNotification 变化:', oldVal, '->', newVal)
})

watch(availableVersion, (newVal) => {
    console.log('📦 availableVersion 变化:', newVal)
})

onMounted(() => {
    initAutoUpdater()
    if (props.autoCheckOnMount) {
        checkForUpdates(false) // 自动检查，不显示"已是最新版本"弹框
    }
})

onUnmounted(() => {
    cleanupListeners()
})

// 初始化自动更新器
const initAutoUpdater = () => {
    console.log('🚀 初始化自动更新器...')
    console.log('electronAPI:', electronAPI)
    console.log('electronAPI.autoUpdater:', electronAPI?.autoUpdater)

    if (!electronAPI || !electronAPI.autoUpdater) {
        console.error('❌ electronAPI 或 autoUpdater 未定义，跳过初始化')
        return
    }

    console.log('✅ electronAPI.autoUpdater 可用，开始监听事件...')

    // 监听自动更新事件
    electronAPI.autoUpdater.onShowUpdateNotification((versionInfo: VersionInfo) => {
        console.log('🔔 收到更新通知:', versionInfo)
        availableVersion.value = versionInfo
        showUpdateNotification.value = true
    })

    electronAPI.autoUpdater.onUpdateAvailable((versionInfo: VersionInfo) => {
        console.log('🎉 收到 onUpdateAvailable 事件:', versionInfo)
        availableVersion.value = versionInfo
        showUpdateNotification.value = true
        console.log('📝 设置 showUpdateNotification = true')
        emit('update-available', versionInfo)
    })

    electronAPI.autoUpdater.onUpdateNotAvailable(() => {
        // 不自动显示"已是最新版本"弹框，只有手动检查时才通过 checkForUpdates 函数显示
        console.log('📝 没有可用更新（来自主进程事件）')
        // showNoUpdate.value = true
        // noUpdateMessage.value = '已是最新版本'
    })

    electronAPI.autoUpdater.onDownloadStart(() => {
        isDownloading.value = true
        downloadError.value = ''
        showDownloadProgress.value = true
    })

    electronAPI.autoUpdater.onDownloadProgress((progress: DownloadProgress) => {
        downloadProgress.value = progress
    })

    electronAPI.autoUpdater.onDownloadComplete(() => {
        isDownloading.value = false
        showDownloadProgress.value = false
        showInstallConfirm.value = true
        if (availableVersion.value) {
            emit('update-downloaded', availableVersion.value)
        }
        toast('新版本已下载完成，可以立即安装')
    })

    electronAPI.autoUpdater.onDownloadError((error: string) => {
        isDownloading.value = false
        downloadError.value = error
        emit('update-error', error)
        toast(`下载失败: ${error}`)
    })

    electronAPI.autoUpdater.onInstallStart(() => {
        isInstalling.value = true
    })

    electronAPI.autoUpdater.onInstallComplete(() => {
        emit('update-installed')
        toast('应用即将重启以完成更新')
    })

    electronAPI.autoUpdater.onInstallError((error: string) => {
        isInstalling.value = false
        emit('update-error', error)
        toast(`安装失败: ${error}`)
    })

    electronAPI.autoUpdater.onInstallCancelled(() => {
        isInstalling.value = false
        toast('安装已取消')
    })

    // 获取当前版本
    electronAPI.getAppVersion().then((version: string) => {
        currentVersion.value = version
    })

    // 延迟自动检查更新（每次组件挂载时执行）
    setTimeout(() => {
        console.log('🔍 延迟自动检查更新（组件初始化后）')
        checkForUpdates(false) // 自动检查，不显示"已是最新版本"弹框
    }, 2000) // 延迟2秒，让界面先完成加载
}

// 检查更新
const checkForUpdates = async (isManualCheck = false) => {
    console.log('🔍 开始检查更新...')
    console.log('electronAPI:', electronAPI)
    console.log('electronAPI.autoUpdater:', electronAPI?.autoUpdater)

    if (!electronAPI) {
        console.error('❌ electronAPI 未定义')
        if (isManualCheck) {
            showNoUpdate.value = true
            noUpdateMessage.value = 'electronAPI 未初始化'
        }
        return
    }

    if (!electronAPI.autoUpdater) {
        console.error('❌ electronAPI.autoUpdater 未定义')
        if (isManualCheck) {
            showNoUpdate.value = true
            noUpdateMessage.value = 'autoUpdater API 未初始化'
        }
        return
    }

    try {
        console.log('📡 调用 checkForUpdates API...')
        const result: UpdateCheckResult = await electronAPI.autoUpdater.checkForUpdates()
        console.log('✅ 检查更新结果:', result)

        if (!result.hasUpdate) {
            console.log('📝 没有更新')
            // 只有手动检查时才显示"已是最新版本"弹框
            if (isManualCheck) {
                showNoUpdate.value = true
                noUpdateMessage.value = '已是最新版本'
            }
        } else if (result.latestVersion) {
            console.log('🎉 发现更新，显示更新对话框:', result.latestVersion)
            availableVersion.value = result.latestVersion
            showUpdateNotification.value = true
            emit('update-available', result.latestVersion)
        }
    } catch (error) {
        console.error('❌ Check for updates failed:', error)
        if (isManualCheck) {
            showNoUpdate.value = true
            noUpdateMessage.value = '检查更新失败'
            toast(`检查更新失败: ${error instanceof Error ? error.message : '未知错误'}`)
        }
    }
}

// 开始下载
const handleStartDownload = async (versionInfo: VersionInfo) => {
    if (!electronAPI?.autoUpdater?.downloadUpdate) {
        console.error('❌ ElectronAPI 或 downloadUpdate 方法不可用')
        downloadError.value = '更新功能不可用'
        return
    }

    try {
        // 确保传递的对象是可序列化的，只包含必要的属性
        const serializableVersionInfo = {
            version: versionInfo.version,
            releaseNotes: versionInfo.releaseNotes,
            releaseDate: versionInfo.releaseDate,
            downloadUrl: versionInfo.downloadUrl,
            fileSize: versionInfo.fileSize,
            checksum: versionInfo.checksum || undefined
        }

        console.log('📥 开始下载更新:', serializableVersionInfo)

        // 清空之前的错误信息
        downloadError.value = ''

        // 调用下载方法
        const success = await electronAPI.autoUpdater.downloadUpdate(serializableVersionInfo)

        if (!success) {
            downloadError.value = '下载启动失败'
            console.error('❌ 下载启动失败')
        } else {
            console.log('✅ 下载已开始')
        }

    } catch (error) {
        console.error('❌ 启动下载时出错:', error)
        downloadError.value = error instanceof Error ? error.message : '下载启动失败'
    }
}

// 重试下载
const handleRetryDownload = () => {
    if (availableVersion.value) {
        handleStartDownload(availableVersion.value)
    }
}

// 取消下载
const handleCancelDownload = () => {
    // 目前不支持取消下载，但关闭对话框
    showDownloadProgress.value = false
    isDownloading.value = false
}

// 开始安装
const handleStartInstall = () => {
    electronAPI.autoUpdater.installUpdate()
}

// 更新通知对话框事件
const handleUpdateLater = () => {
    // 用户选择稍后更新，什么也不做
}

const handleNotificationClose = () => {
    // 关闭通知对话框
}

// 下载对话框事件
const handleDownloadClose = () => {
    if (!isDownloading.value) {
        showDownloadProgress.value = false
    }
}

// 安装对话框事件
const handleInstallLater = () => {
    // 用户选择稍后安装
}

const handleInstallClose = () => {
    if (!isInstalling.value) {
        showInstallConfirm.value = false
    }
}

// 无更新对话框事件
const handleNoUpdateClose = () => {
    showNoUpdate.value = false
}

// 清理监听器
const cleanupListeners = () => {
    electronAPI.autoUpdater.removeAllListeners()
}

// 手动检查更新（用于外部调用，会显示所有结果）
const checkForUpdatesManual = () => {
    return checkForUpdates(true) // 手动检查，显示所有结果包括"已是最新版本"
}

// 暴露方法给父组件调用
defineExpose({
    checkForUpdates: checkForUpdatesManual
})
</script>