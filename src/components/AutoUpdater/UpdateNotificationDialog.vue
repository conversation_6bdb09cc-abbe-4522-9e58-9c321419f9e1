<template>
    <Dialog v-model:open="isOpen">
        <DialogContent class="sm:max-w-[400px] p-0 gap-0" @pointer-down-outside="$event.preventDefault()"
            @interact-outside="$event.preventDefault()" @escape-key-down="$event.preventDefault()">
            <!-- 关闭按钮 -->
            <div class="absolute right-4 top-4 z-10">
                <button @click="handleClose" class="text-gray-400 hover:text-gray-600 transition-colors">
                </button>
            </div>

            <!-- 内容区域 -->
            <div class="flex flex-col items-center text-center p-8">
                <!-- 图标 -->
                <div class="w-16 h-16 rounded-full flex items-center justify-center mb-4">
                    <img src="./icon1.png" alt="星图图标" class="w-16 h-16 rounded-full" />
                </div>

                <!-- 标题 -->
                <h2 class="text-lg font-medium text-gray-900 mb-2">星图</h2>
                <div class="text-sm text-gray-600 mb-6 space-y-1">
                    <p>检测到有新版本，建议及时更新</p>
                    <div v-if="versionInfo" class="space-y-1">
                        <p><span class="font-medium">当前版本：</span>{{ currentVersion }}</p>
                        <p><span class="font-medium">最新版本：</span>{{ versionInfo.version }}</p>
                    </div>
                </div>

                <!-- 发行说明（如果有的话） -->
                <div v-if="versionInfo?.releaseNotes"
                    class="w-full p-3 bg-blue-50 border border-blue-200 rounded-md mb-6">
                    <h4 class="text-sm font-medium text-blue-800 mb-2">更新内容</h4>
                    <p class="text-sm text-blue-700 whitespace-pre-line">{{ versionInfo.releaseNotes }}</p>
                </div>

                <!-- 按钮组 -->
                <div class="flex gap-3 w-full">
                    <button @click="handleUpdateLater"
                        class="flex-1 px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                        暂不更新
                    </button>
                    <button @click="handleUpdateNow"
                        class="flex-1 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors">
                        立即更新
                    </button>
                </div>
            </div>
        </DialogContent>
    </Dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Dialog, DialogContent } from '@/components/ui/dialog'
import type { VersionInfo } from '../../types/electron'

interface Props {
    open?: boolean
    versionInfo?: VersionInfo
    currentVersion?: string
}

interface Emits {
    'update:open': [value: boolean]
    'update-now': [versionInfo: VersionInfo]
    'update-later': []
    'close': []
}

const props = withDefaults(defineProps<Props>(), {
    open: false,
    currentVersion: '1.0.0'
})

const emit = defineEmits<Emits>()

const isOpen = computed({
    get: () => props.open,
    set: (value: boolean) => emit('update:open', value)
})

const handleUpdateNow = () => {
    if (props.versionInfo) {
        emit('update-now', props.versionInfo)
    }
    isOpen.value = false
}

const handleUpdateLater = () => {
    emit('update-later')
    isOpen.value = false
}

const handleClose = () => {
    emit('close')
    isOpen.value = false
}
</script>