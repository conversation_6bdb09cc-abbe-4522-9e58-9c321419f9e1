<template>
    <div class="grid-view-container">
        <!-- 表头区域 -->
        <div class="pb-3 grid-header" v-if="selectedItems.size > 0 || showHeader">
            <div class="flex gap-4 items-center">
                <!-- 全选复选框和计数 -->
                <div class="flex gap-2 items-center">
                    <Checkbox :model-value="checkboxState" @update:model-value="toggleSelectAll" />
                    <span class="text-sm text-muted-foreground">
                        {{ selectedItems.size > 0 ? `已选择 ${selectedItems.size}/${items.length} 项` : '全选' }}
                    </span>
                </div>

                <!-- 操作按钮组 -->
                <TrashActionButtonGroup :selected-count="selectedItems.size" :selected-items="selectedItemsArray"
                    :category-id="categoryId" @restore="handleRestore" @permanently-delete="handlePermanentlyDelete"
                    @restore-success="handleRestoreSuccess" @delete-success="handleDeleteSuccess"
                    @refresh-directory="handleRefreshDirectory" />
            </div>
        </div>

        <!-- 网格区域 -->
        <div class="grid-view">
            <template v-for="item in items" :key="item.id">
                <FolderItem v-if="item.type === 'folder'" :item="item as FolderItemType"
                    :is-selected="selectedItems.has(item.id)" :show-checkbox="selectedItems.size > 0 || showCheckboxes"
                    :is-renaming="renamingItemId === item.id" :data-id="item.id" @click="handleFolderClick"
                    @dblclick="handleFolderDoubleClick" @contextmenu="handleItemContextMenu" @select="handleItemSelect"
                    @rename="handleItemRename" @rename-cancel="handleItemRenameCancel" />
                <FileItem v-else :item="item as FileItemType" :is-selected="selectedItems.has(item.id)"
                    :show-checkbox="selectedItems.size > 0 || showCheckboxes" :is-renaming="renamingItemId === item.id"
                    :data-id="item.id" @click="handleFileClick" @dblclick="handleFileDoubleClick"
                    @contextmenu="handleItemContextMenu" @select="handleItemSelect" @rename="handleItemRename"
                    @rename-cancel="handleItemRenameCancel" />
            </template>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, watch, ref } from 'vue'
import FileItem from '../Folders/FilesView/FileItem.vue'
import FolderItem from '../Folders/FilesView/FolderItem.vue'
import TrashActionButtonGroup from './TrashActionButtonGroup.vue'
import { Checkbox } from '@/components/ui/checkbox'
import type { FileItemType, FolderItemType, ItemType } from '@/types/files'

// Props
const props = defineProps<{
    items: ItemType[]
    showHeader?: boolean
    showCheckboxes?: boolean
    selectedItems?: Set<string>
    categoryId: string
}>()

// Emits
const emit = defineEmits<{
    fileClick: [item: FileItemType]
    fileDoubleClick: [item: FileItemType]
    folderClick: [item: FolderItemType]
    folderDoubleClick: [item: FolderItemType]
    contextmenu: [event: MouseEvent, item: ItemType]
    selectionChange: [selectedIds: Set<string>]
    restore: [selectedIds: Set<string>]
    permanentlyDelete: [selectedIds: Set<string>]
    rename: [itemId: string, newName: string, item: ItemType]
    refreshDirectory: []
}>()

// 选择状态 - 使用父组件传入的选择状态或本地状态
const selectedItems = computed(() => props.selectedItems || new Set<string>())

// 计算选中的项目数组
const selectedItemsArray = computed(() => {
    return props.items.filter(item => selectedItems.value.has(item.id))
})

// 重命名状态
const renamingItemId = ref<string | null>(null)

// 监听 items 变化，清空选择状态和重命名状态
watch(
    () => props.items,
    () => {
        if (selectedItems.value.size > 0) {
            emit('selectionChange', new Set())
        }
        renamingItemId.value = null
    },
    { deep: true }
)

// 计算 Checkbox 的三种状态
const checkboxState = computed<boolean | 'indeterminate'>(() => {
    if (props.items.length === 0) return false
    if (selectedItems.value.size === 0) return false
    if (selectedItems.value.size === props.items.length) return true
    return 'indeterminate'
})

// 全选/取消全选
const toggleSelectAll = (checked: boolean | 'indeterminate') => {
    if (checked === true) {
        const allIds = new Set(props.items.map(item => item.id))
        emit('selectionChange', allIds)
    } else {
        emit('selectionChange', new Set())
    }
}

// 处理项目选择
const handleItemSelect = (id: string, selected: boolean) => {
    const newSelected = new Set(selectedItems.value)
    if (selected) {
        newSelected.add(id)
    } else {
        newSelected.delete(id)
    }
    emit('selectionChange', newSelected)
}

// 处理文件点击
const handleFileClick = (item: FileItemType) => {
    emit('fileClick', item)
}

// 处理文件双击
const handleFileDoubleClick = (item: FileItemType) => {
    emit('fileDoubleClick', item)
}

// 处理文件夹点击
const handleFolderClick = (item: FolderItemType) => {
    emit('folderClick', item)
}

// 处理文件夹双击
const handleFolderDoubleClick = (item: FolderItemType) => {
    emit('folderDoubleClick', item)
}

// 处理右键菜单
const handleItemContextMenu = (event: MouseEvent, item: ItemType) => {
    emit('contextmenu', event, item)
}

// 处理还原操作
const handleRestore = () => {
    if (selectedItems.value.size === 0) return
    emit('restore', selectedItems.value)
}

// 处理彻底删除操作
const handlePermanentlyDelete = () => {
    if (selectedItems.value.size === 0) return
    emit('permanentlyDelete', selectedItems.value)
}

// 处理还原成功
const handleRestoreSuccess = (_restoredItems: ItemType[]) => {
    // 清除选择
    emit('selectionChange', new Set())
}

// 处理删除成功
const handleDeleteSuccess = (_deletedItems: ItemType[]) => {
    // 清除选择
    emit('selectionChange', new Set())
}

// 处理刷新目录
const handleRefreshDirectory = () => {
    emit('refreshDirectory')
}

// 处理重命名
const handleItemRename = (itemId: string, newName: string) => {
    const item = props.items.find(item => item.id === itemId)
    if (item) {
        renamingItemId.value = null
        emit('rename', itemId, newName, item)
    }
}

// 处理重命名取消
const handleItemRenameCancel = () => {
    renamingItemId.value = null
}

// 开始重命名
const startRename = (itemId: string) => {
    renamingItemId.value = itemId
}

// 暴露方法给父组件
defineExpose({
    startRename
})
</script>

<style scoped>
.grid-view-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.grid-header {
    border-bottom: 1px solid hsl(var(--border));
    background-color: hsl(var(--card));
    margin-bottom: 0.5rem;
}

.grid-view {
    display: grid;
    grid-template-columns: repeat(auto-fit, 150px);
    grid-template-rows: repeat(auto-fit, 150px);
    gap: 1rem;
    padding: 0.5rem;
    justify-content: start;
    overflow: auto;
    flex: 1;
}
</style>