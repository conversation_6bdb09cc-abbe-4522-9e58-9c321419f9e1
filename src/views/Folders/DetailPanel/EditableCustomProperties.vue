<template>
  <div class="p-4 space-y-3 border-b">
    <div class="flex justify-between items-center">
      <h5 class="text-sm font-medium text-muted-foreground">自定义属性</h5>

      <!-- 编辑控制按钮 -->
      <div class="flex gap-2">
        <Button v-if="!isEditing && editableProperties.length > 0" variant="ghost" size="sm" @click="startEditing"
          class="px-2 h-7 text-xs">
          <Edit class="mr-1 w-3 h-3" />
          编辑
        </Button>

        <template v-if="isEditing">
          <Button variant="ghost" size="sm" @click="cancelEditing" :disabled="isSaving" class="px-2 h-7 text-xs">
            <X class="mr-1 w-3 h-3" />
            取消
          </Button>
          <Button variant="default" size="sm" @click="saveEditing" :disabled="isSaving" class="px-2 h-7 text-xs">
            <Check v-if="!isSaving" class="mr-1 w-3 h-3" />
            <Loader2 v-else class="mr-1 w-3 h-3 animate-spin" />
            保存
          </Button>
        </template>
      </div>
    </div>

    <!-- 属性列表 -->
    <div class="space-y-3">
      <div v-for="(value, key) in properties" :key="key" class="grid grid-cols-[auto_1fr] gap-3 items-center">
        <!-- 属性标签 - 自适应宽度，不换行 -->
        <span class="text-sm whitespace-nowrap text-muted-foreground">
          {{ formatPropertyKey(key) }}
        </span>

        <!-- 属性值 - 查看模式或编辑模式 -->
        <div class="flex-1">
          <!-- 查看模式 -->
          <span v-if="!isEditing" class="text-sm font-medium text-right break-words" :class="getValueClass(value)">
            {{ formatPropertyValue(value) }}
          </span>

          <!-- 编辑模式 -->
          <div v-else-if="getEditableProperty(key)" class="w-full">
            <!-- 单选下拉框 -->
            <Select v-if="getEditableProperty(key)?.upload_type === 'select'"
              :model-value="String(editingProperties[getEditableProperty(key)!.field_name] || '')"
              @update:model-value="(val) => updateEditingProperty(getEditableProperty(key)!.field_name, val)">
              <SelectTrigger class="h-8 text-xs">
                <SelectValue placeholder="请选择" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="option in getEditableProperty(key)!.options" :key="option.value"
                  :value="String(option.value)">
                  {{ option.name }}
                </SelectItem>
              </SelectContent>
            </Select>

            <!-- 多选下拉框 -->
            <Popover v-else-if="getEditableProperty(key)?.upload_type === 'multiselect'">
              <PopoverTrigger as-child>
                <Button variant="outline" class="justify-between py-1 w-full h-8 text-xs font-normal">
                  <div class="flex overflow-hidden flex-1 items-center">
                    <div v-if="!getSelectedValues(getEditableProperty(key)!.field_name).length"
                      class="text-foreground/100">
                      请选择
                    </div>
                    <div v-else class="flex overflow-hidden gap-1 items-center">
                      <!-- 显示选中的标签 -->
                      <div v-for="selectedName in getSelectedNames(getEditableProperty(key)!, 2)" :key="selectedName"
                        class="inline-flex gap-1 items-center px-1 py-0.5 text-xs rounded bg-secondary text-secondary-foreground shrink-0">
                        <span class="truncate max-w-12" :title="selectedName">{{ selectedName }}</span>
                        <button @click.stop="removeSelectedByName(getEditableProperty(key)!, selectedName)"
                          class="hover:text-destructive">
                          <X class="w-2 h-2" />
                        </button>
                      </div>
                      <!-- 显示剩余数量 -->
                      <div v-if="getSelectedValues(getEditableProperty(key)!.field_name).length > 2"
                        class="text-xs text-muted-foreground shrink-0">
                        +{{ getSelectedValues(getEditableProperty(key)!.field_name).length - 2 }}
                      </div>
                    </div>
                  </div>
                  <ChevronDown class="ml-1 w-3 h-3 opacity-50 shrink-0" />
                </Button>
              </PopoverTrigger>
              <PopoverContent class="w-[--radix-popover-trigger-width] p-0" align="start">
                <!-- 选项列表 -->
                <div class="overflow-y-auto max-h-48">
                  <div v-if="!getEditableProperty(key)!.options.length" class="px-3 py-2 text-sm text-muted-foreground">
                    暂无选项
                  </div>
                  <div v-for="option in getEditableProperty(key)!.options" :key="option.value"
                    class="flex justify-between items-center px-3 py-2 text-sm cursor-pointer hover:bg-accent"
                    @click="toggleOption(getEditableProperty(key)!.field_name, String(option.value))">
                    <span>{{ option.name }}</span>
                    <Check v-if="isOptionSelected(getEditableProperty(key)!.field_name, String(option.value))"
                      class="w-4 h-4 text-primary" />
                  </div>
                </div>

                <!-- 底部状态栏 -->
                <div v-if="getSelectedValues(getEditableProperty(key)!.field_name).length > 0"
                  class="flex justify-between items-center px-3 py-2 text-xs border-t bg-muted/30">
                  <span class="text-muted-foreground">已选择 {{
                    getSelectedValues(getEditableProperty(key)!.field_name).length
                  }} 项</span>
                  <button @click="clearAllSelections(getEditableProperty(key)!.field_name)"
                    class="text-destructive hover:underline">
                    清空
                  </button>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          <!-- 非可编辑字段在编辑模式下的显示 -->
          <span v-else-if="isEditing" class="text-sm font-medium text-right break-words text-muted-foreground">
            {{ formatPropertyValue(value) }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Popover, PopoverTrigger, PopoverContent } from '@/components/ui/popover'
import { Edit, X, Check, ChevronDown, Loader2 } from 'lucide-vue-next'
import type { EditableProperty } from './useCustomProperties'

// Props
const props = defineProps<{
  properties: Record<string, any>
  isEditing: boolean
  editingProperties: Record<string, any>
  isSaving: boolean
  editableProperties: EditableProperty[]
  startEditing: () => void
  cancelEditing: () => void
  saveEditing: () => Promise<void>
  updateEditingProperty: (fieldName: string, value: any) => void
  formatPropertyValue: (value: any) => string
  getValueClass: (value: any) => string
}>()

// 格式化属性键名
const formatPropertyKey = (key: string): string => {
  // 直接返回键名，因为传入的已经是处理过的标签
  return key
}

// 根据标签获取可编辑属性，只对多选字段过滤掉"未选择"选项
const getEditableProperty = (label: string): EditableProperty | undefined => {
  const property = props.editableProperties.find(prop => prop.label === label)
  if (!property) return undefined

  // 只对多选字段过滤掉"未选择"选项
  if (property.upload_type === 'multiselect') {
    return {
      ...property,
      options: property.options.filter(option => option.name !== '未选择')
    }
  }

  // 单选字段和其他类型保持原样
  return property
}

// 获取选中的值，只对多选字段过滤掉"未选择"的值
const getSelectedValues = (fieldName: string): string[] => {
  const value = props.editingProperties[fieldName]
  const values = Array.isArray(value) ? value.map(String) : []

  // 找到对应的属性配置
  const property = props.editableProperties.find(prop => prop.field_name === fieldName)
  if (!property) return values

  // 只对多选字段过滤掉"未选择"选项对应的值
  if (property.upload_type === 'multiselect') {
    const unselectedOption = property.options.find(opt => opt.name === '未选择')
    if (unselectedOption) {
      return values.filter(v => v !== String(unselectedOption.value))
    }
  }

  return values
}

// 检查选项是否被选中
const isOptionSelected = (fieldName: string, optionValue: string): boolean => {
  return getSelectedValues(fieldName).includes(optionValue)
}

// 获取已选择选项的名称
const getSelectedNames = (property: EditableProperty, limit?: number): string[] => {
  const selectedValues = getSelectedValues(property.field_name)
  const names: string[] = []

  selectedValues.forEach(value => {
    const option = property.options.find(opt => String(opt.value) === value)
    if (option?.name) {
      names.push(option.name)
    }
  })

  return limit ? names.slice(0, limit) : names
}

// 切换选项
const toggleOption = (fieldName: string, optionValue: string) => {
  const currentValues = getSelectedValues(fieldName)
  const newValues = currentValues.includes(optionValue)
    ? currentValues.filter(v => v !== optionValue)
    : [...currentValues, optionValue]

  props.updateEditingProperty(fieldName, newValues)
}

// 根据名称移除选项
const removeSelectedByName = (property: EditableProperty, optionName: string) => {
  const option = property.options.find(opt => opt.name === optionName)
  if (option) {
    toggleOption(property.field_name, String(option.value))
  }
}

// 清空所有选择
const clearAllSelections = (fieldName: string) => {
  props.updateEditingProperty(fieldName, [])
}
</script>
