<template>
  <div class="flex flex-col min-h-full max-h-full">
    <!-- 主文件区域 - 整体可滚动 -->
    <div ref="fileAreaRef" class="flex overflow-y-auto overflow-x-hidden relative flex-1 py-1 min-w-0 file-area" :class="[
      isDetailPanelVisible ? 'pr-2 mr-2' : '',
      isDragging ? 'drag-over' : ''
    ]" @dragenter="handleDragEnter" @dragover="handleDragOver" @dragleave="handleDragLeave" @drop="handleDrop">

      <!-- 拖拽上传遮罩 -->
      <div v-if="isDragging"
        class="flex absolute inset-0 z-50 justify-center items-center rounded-lg border-2 border-dashed bg-primary/10 border-primary">
        <div class="text-center">
          <Upload class="mx-auto mb-2 w-12 h-12 text-primary" />
          <p class="text-lg font-medium text-primary">拖拽文件到此处上传</p>
          <p class="text-sm text-muted-foreground">支持大量文件智能打包上传</p>
        </div>
      </div>

      <!-- 文件显示区域 -->
      <div class="flex flex-col flex-1 min-w-0">
        <div class="flex-1" v-if="items.length > 0">
          <!-- 网格视图 -->
          <GridView v-if="viewMode === 'grid'" ref="gridViewRef" :items="items" :show-checkboxes="true"
            :show-header="true" :selected-items="selectedIds" :is-downloading="isDownloading"
            @file-click="handleFileClick" @file-double-click="handleFileDoubleClick" @folder-click="handleFolderClick"
            @folder-double-click="handleFolderDoubleClick" @contextmenu="handleContextMenu"
            @selection-change="handleSelectionChange" @download="handleDownload" @rename="handleRename"
            @refresh-directory="handleRefreshDirectory" />

          <!-- 列表视图 -->
          <ListView v-else ref="listViewRef" :items="items" :selected-items="selectedIds"
            :is-downloading="isDownloading" @file-click="handleFileClick" @file-double-click="handleFileDoubleClick"
            @folder-click="handleFolderClick" @folder-double-click="handleFolderDoubleClick"
            @contextmenu="handleContextMenu" @selection-change="handleSelectionChange" @download="handleDownload"
            @rename="handleRename" @refresh-directory="handleRefreshDirectory" />
        </div>

        <!-- 分页组件 -->
        <FilePagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total-items="totalItems"
          @page-change="handlePageChange" @page-size-change="handlePageSizeChange" />

        <!-- 空状态 -->
        <div v-if="totalItems === 0" class="flex-1 py-12 text-center">
          <component :is="emptyIcon" class="mx-auto mb-4 w-16 h-16 text-muted-foreground/50" />
          <h3 class="mb-2 text-lg font-medium text-muted-foreground">{{ emptyTitle }}</h3>
          <p class="mb-4 text-sm text-muted-foreground">{{ emptyMessage }}</p>
          <button
            class="inline-flex items-center px-4 py-2 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90"
            @click="handleUploadClick">
            <Upload class="mr-2 w-4 h-4" />
            {{ uploadButtonText }}
          </button>
        </div>

        <!-- 右键菜单 -->
        <ContextMenu :show="contextMenu.show" :x="contextMenu.x" :y="contextMenu.y" :item="contextMenu.item"
          :selected-items="getSelectedItems()" @close="hideContextMenu" @open-folder="openFolder"
          @download="handleContextDownload" @batch-download="handleContextBatchDownload" @rename="startRenameItem"
          @share="shareItem" @refresh-directory="handleRefreshDirectory" />
      </div>

      <!-- 详细信息面板 -->
      <Transition name="detail-panel-container" enter-active-class="detail-panel-container-enter-active"
        leave-active-class="detail-panel-container-leave-active" enter-from-class="detail-panel-container-enter-from"
        leave-to-class="detail-panel-container-leave-to">
        <div v-if="isDetailPanelVisible" class="sticky top-0 w-80 max-h-screen border-l z-10 bg-background">
          <DetailPanel :selected-item="selectedItem" :custom-properties="computedCustomProperties || undefined"
            :is-editing="isEditing" :editing-properties="editingProperties" :is-saving="isSaving"
            :editable-properties="editableProperties" :start-editing="startEditing" :cancel-editing="cancelEditing"
            :save-editing="saveEditing" :update-editing-property="updateEditingProperty"
            :format-property-value="formatPropertyValue" :get-value-class="getValueClass" @close="hideDetailPanel" />
        </div>
      </Transition>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, computed, ref, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { Upload } from 'lucide-vue-next'
import GridView from './GridView.vue'
import ListView from './ListView.vue'
import DetailPanel from '../DetailPanel/index.vue'
import ContextMenu from './ContextMenu.vue'
import FilePagination from './Pagination.vue'
import { useRectangleSelection } from '@/composables/useRectangleSelection'
import { useCustomProperties, type ApiFilterGroup } from '../DetailPanel/useCustomProperties'
import { useBaseRename } from './useRename'
import { useDragAndDrop, type DragDropResult } from '@/composables/useDragAndDrop'
import { toast } from 'vue-sonner'

import type { ItemType, FileItemType, FolderItemType } from '@/types/files'

// Props
const props = withDefaults(defineProps<{
  items: ItemType[]
  viewMode: 'grid' | 'list'
  emptyIcon?: any
  emptyTitle?: string
  emptyMessage?: string
  uploadButtonText?: string
  apiFilterOptions?: ApiFilterGroup[]
  pageSize: number
  totalItems: number
  isDownloading?: boolean
}>(), {
  isDownloading: false,
  apiFilterOptions: () => [],
  totalItems: 0
})

// Emits
const emit = defineEmits<{
  fileClick: [item: FileItemType]
  fileDoubleClick: [item: FileItemType]
  folderClick: [item: FolderItemType]
  upload: []
  dragUpload: [files: File[]]
  itemRenamed: [itemId: string, newName: string, item: ItemType]
  itemAction: [itemAction: { action: string, item: ItemType }]
  batchAction: [batchAction: { action: string, items: ItemType[] }]
  deleteSuccess: []
  refreshDirectory: []
  pageChange: [page: number]
  pageSizeChange: [size: number]
}>()

// 路由
const router = useRouter()

// 重命名功能
const { executeRename } = useBaseRename()

// 分页状态 - 简化为基本状态管理
const currentPage = ref(1)
const pageSize = ref(props.pageSize)

// 详细信息面板状态
const isDetailPanelVisible = ref(false)
const selectedItem = ref<ItemType | null>(null)

// 多选状态
const selectedIds = ref<Set<string>>(new Set())

// 主文件区域引用
const fileAreaRef = ref<HTMLElement | null>(null)
const gridViewRef = ref<InstanceType<typeof GridView> | null>(null)
const listViewRef = ref<InstanceType<typeof ListView> | null>(null)

// 右键菜单
const contextMenu = reactive({
  show: false,
  x: 0,
  y: 0,
  item: null as ItemType | null
})

// 分页事件处理 - 简化逻辑
const handlePageChange = (page: number) => {
  currentPage.value = page
  selectedIds.value.clear()
  emit('pageChange', page)
}

const handlePageSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1 // 重置到第一页
  selectedIds.value.clear()
  emit('pageSizeChange', size)
}

// 监听数据变化时清空选中状态
watch(() => props.items, () => {
  selectedIds.value.clear()
}, { flush: 'sync' })

// 监听pageSize props变化
watch(() => props.pageSize, (newPageSize) => {
  pageSize.value = newPageSize
})

// 详细信息面板控制
const showDetailPanelFn = (item: ItemType) => {
  selectedItem.value = {
    ...item,
    path: item.type === 'folder' ? (item as any).path : undefined
  }
  isDetailPanelVisible.value = true
}

const hideDetailPanel = () => {
  isDetailPanelVisible.value = false
  selectedItem.value = null
}

// 重置详情面板，包括隐藏面板和重置所有状态
const resetDetailPanel = () => {
  // 如果正在编辑，取消编辑
  if (isEditing.value) {
    cancelEditing()
  }

  // 隐藏详情面板并清除选中项
  isDetailPanelVisible.value = false
  selectedItem.value = null

  // 确保清空所有相关状态
  editingProperties.value = {}
  isSaving.value = false
}

// 使用自定义属性 composable
const {
  customProperties: computedCustomProperties,
  isEditing,
  editingProperties,
  isSaving,
  editableProperties,
  startEditing,
  cancelEditing,
  saveEditing,
  updateEditingProperty,
  formatPropertyValue,
  getValueClass
} = useCustomProperties(
  computed(() => selectedItem.value),
  computed(() => props.apiFilterOptions),
  async () => {
    // 保存成功后刷新目录列表
    emit('refreshDirectory')
  }
)

// 拖拽上传功能
const {
  isDragging,
  handleDragEnter,
  handleDragOver,
  handleDragLeave,
  handleDrop
} = useDragAndDrop(
  {
    allowDirectories: true,
    maxFiles: Infinity,
    useElectronAPI: true
  },
  {
    onFilesProcessed: (result: DragDropResult) => {
      if (result.files.length > 0) {
        const files = result.files.map(f => f.file)
        emit('dragUpload', files)
      }
    },
    onError: (errors: string[]) => {
      errors.forEach(error => toast.error(error))
    },
    onSmartPackTriggered: async (files: File[], fileCount: number) => {
      toast.info(`检测到大量文件 (${fileCount} 个)，正在使用智能打包功能`)
      emit('dragUpload', files)
    }
  }
)

// 事件处理
const handleFileClick = (item: FileItemType) => {
  console.log('打开文件:', item.name)
  // 添加延迟，防止双击时触发详细信息
  setTimeout(() => {
    showDetailPanelFn(item)
  }, 300)
  emit('fileClick', item)
}

const handleFileDoubleClick = (item: FileItemType) => {
  console.log('双击文件:', item.name)
  // 双击时不显示详细信息，只发出事件
  emit('fileDoubleClick', item)
}

const handleFolderClick = (item: FolderItemType) => {
  console.log('选择文件夹:', item.name)
  // 添加延迟，防止双击时触发详细信息
  setTimeout(() => {
    showDetailPanelFn(item)
  }, 300)
}

const handleFolderDoubleClick = (item: FolderItemType) => {
  console.log('进入文件夹:', item.name)

  if (item.path) {
    router.push(item.path)
  } else {
    // 双击时不显示详细信息，只发出事件
    emit('folderClick', item)
  }
}

const handleContextMenu = (event: MouseEvent, item: ItemType) => {
  contextMenu.show = true
  contextMenu.x = event.clientX
  contextMenu.y = event.clientY
  contextMenu.item = item
}

// 重命名处理
const handleRename = async (itemId: string, newName: string, item: ItemType) => {
  const success = await executeRename(item, newName, props.items)

  if (success) {
    const targetItem = props.items.find(item => item.id === itemId)
    if (targetItem) {
      targetItem.name = newName.trim()
      emit('itemRenamed', itemId, newName.trim(), item)

      if (selectedItem.value && selectedItem.value.id === itemId) {
        selectedItem.value.name = newName.trim()
      }
    }
  }
}

const startRenameItem = (item: ItemType) => {
  hideContextMenu()

  if (props.viewMode === 'grid' && gridViewRef.value) {
    gridViewRef.value.startRename(item.id)
  } else if (props.viewMode === 'list' && listViewRef.value) {
    listViewRef.value.startRename(item.id)
  }
}

// 框选功能
const { setSelectedItems } = useRectangleSelection({
  containerRef: fileAreaRef,
  itemSelector: '[data-id]',
  getItemId: (element) => {
    return element.dataset.id || null
  },
  onSelectionChange: (newSelectedIds) => {
    selectedIds.value = newSelectedIds
  }
})

const handleSelectionChange = (newSelectedIds: Set<string>) => {
  selectedIds.value = newSelectedIds
  setSelectedItems(newSelectedIds)
}

// 处理下载操作
const handleDownload = () => {
  if (selectedIds.value.size === 0) return

  if (selectedIds.value.size === 1) {
    const itemId = Array.from(selectedIds.value)[0]
    const item = props.items.find(item => item.id === itemId)
    if (item) {
      emit('itemAction', { action: 'download', item })
    }
  } else {
    const selectedItems = props.items.filter(item => selectedIds.value.has(item.id))
    emit('batchAction', { action: 'download', items: selectedItems })
  }

  selectedIds.value.clear()
}

const handleContextDownload = (item: ItemType) => {
  emit('itemAction', { action: 'download', item })
}

const handleContextBatchDownload = (items: ItemType[]) => {
  emit('batchAction', { action: 'download', items })
}

const getSelectedItems = (): ItemType[] => {
  return props.items.filter(item => selectedIds.value.has(item.id))
}

const handleUploadClick = () => {
  emit('upload')
}

const hideContextMenu = () => {
  contextMenu.show = false
  contextMenu.item = null
}

const openFolder = (item: FolderItemType) => {
  handleFolderDoubleClick(item)
  hideContextMenu()
}

const shareItem = (item: ItemType) => {
  console.log('分享:', item.name)
  hideContextMenu()
}

const handleRefreshDirectory = () => {
  emit('refreshDirectory')
}

// 生命周期
onMounted(() => {
  document.addEventListener('click', hideContextMenu)
})

onUnmounted(() => {
  document.removeEventListener('click', hideContextMenu)
})

// 暴露必要的方法给父组件
defineExpose({
  // 基本状态
  getCurrentPage: () => currentPage.value,
  getPageSize: () => pageSize.value,

  // 选中状态
  getSelectedItems: () => Array.from(selectedIds.value),
  clearSelection: () => selectedIds.value.clear(),

  // 详情面板
  showDetailPanel: showDetailPanelFn,
  hideDetailPanel,
  resetDetailPanel,
})
</script>

<style scoped>
/* 详细信息面板容器动画 */
.detail-panel-container-enter-active,
.detail-panel-container-leave-active {
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.detail-panel-container-enter-from {
  width: 0;
  opacity: 0;
  transform: translateX(50px);
}

.detail-panel-container-leave-to {
  width: 0;
  opacity: 0;
  transform: translateX(50px);
}

.detail-panel-container-enter-active,
.detail-panel-container-leave-active {
  will-change: width, opacity, transform;
  overflow: hidden;
}

.file-area {
  position: relative;
  user-select: none;
}

.file-area:has(.rectangle-selection) * {
  transition: none !important;
}

.file-area[class*="drag-over"] {
  background-color: rgba(var(--primary), 0.05);
}

.file-area {
  transition: background-color 0.2s ease;
}
</style>