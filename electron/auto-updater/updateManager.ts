import { app, dialog } from 'electron'
import { EventEmitter } from 'events'
import { autoUpdater } from 'electron-updater'
import type { VersionInfo, UpdateCheckResult, DownloadProgress, UpdateStatus } from './types'
import { createLogger } from '../logger'

const logger = createLogger('AutoUpdater')

export class UpdateManager extends EventEmitter {
  private currentVersion: string
  private status: UpdateStatus = { status: 'not-available' }
  private isCheckingForUpdate = false
  private isDownloading = false
  private isManualCheck = false // 标识是否为手动检测

  constructor() {
    super()
    this.currentVersion = app.getVersion()
    
    // 配置 autoUpdater
    this.setupAutoUpdater()
  }

  private setupAutoUpdater() {
    // 设置更新服务器
    // autoUpdater.setFeedURL({
    //   provider: 'generic',
    //   url: 'http://172.20.22.137:8000/packages/update-files/'
    // })

    // 禁用自动下载，让用户手动选择
    autoUpdater.autoDownload = false
    autoUpdater.autoInstallOnAppQuit = false

    // 监听官方事件，转换为我们的事件格式
    autoUpdater.on('checking-for-update', () => {
      logger.info('Checking for update...')
      this.status = { status: 'checking' }
      this.emit('update-check-start')
    })

    autoUpdater.on('update-available', (info: any) => {
      logger.info('Update available:', info.version)
      
      // 转换为我们的格式，处理releaseNotes可能是数组的情况
      let releaseNotes = Array.isArray(info.releaseNotes) 
        ? info.releaseNotes.map((note: any) => typeof note === 'string' ? note : note.note || '').join('\n')
        : (info.releaseNotes || '更新日志暂无')
      
      // 处理字符串中的转义字符
      if (typeof releaseNotes === 'string') {
        releaseNotes = releaseNotes.replace(/\\n/g, '\n')
      }

      const versionInfo: VersionInfo = {
        version: info.version,
        releaseNotes,
        releaseDate: info.releaseDate || new Date().toISOString(),
        downloadUrl: '', // autoUpdater 内部处理
        fileSize: info.files?.[0]?.size || 0,
        checksum: info.files?.[0]?.sha512 || ''
      }

      this.status = { 
        status: 'available',
        version: versionInfo
      }

      const result: UpdateCheckResult = {
        hasUpdate: true,
        currentVersion: this.currentVersion,
        latestVersion: versionInfo
      }

      this.emit('update-available', versionInfo)
      this.emit('update-check-result', result)
    })

    autoUpdater.on('update-not-available', () => {
      logger.info('Update not available')
      this.status = { status: 'not-available' }
      
      const result: UpdateCheckResult = {
        hasUpdate: false,
        currentVersion: this.currentVersion
      }

      // 只有手动检测时才发送 update-not-available 事件
      if (this.isManualCheck) {
        this.emit('update-not-available')
      }
      this.emit('update-check-result', result)
    })

    autoUpdater.on('error', (error) => {
      logger.error('AutoUpdater error:', error)
      this.status = { 
        status: 'error',
        error: error.message 
      }
      this.emit('update-error', error.message)
      this.emit('update-check-result', {
        hasUpdate: false,
        currentVersion: this.currentVersion,
        error: error.message
      })
    })

    autoUpdater.on('download-progress', (progressObj) => {
      logger.info('Download progress:', progressObj)
      
      // 转换为我们的格式
      const progress: DownloadProgress = {
        percent: Math.round(progressObj.percent),
        bytesDownloaded: progressObj.transferred,
        totalBytes: progressObj.total,
        speed: progressObj.bytesPerSecond
      }

      this.status = { 
        status: 'downloading',
        progress
      }

      this.emit('download-progress', progress)
    })

    autoUpdater.on('update-downloaded', (info) => {
      logger.info('Update downloaded:', info.version)
      this.status = { status: 'downloaded' }
      this.isDownloading = false
      this.emit('download-complete', info.downloadedFile)
    })
  }

  // 检查更新
  async checkForUpdates(isManualCheck: boolean = false): Promise<UpdateCheckResult> {
    if (this.isCheckingForUpdate) {
      throw new Error('Update check already in progress')
    }

    this.isCheckingForUpdate = true
    this.isManualCheck = isManualCheck

    try {
      logger.info('Checking for updates, current version:', this.currentVersion)
      
      // 使用官方方法检查更新
      const result = await autoUpdater.checkForUpdates()
      
      if (!result) {
        throw new Error('Failed to check for updates')
      }

      // 正确的版本比较逻辑
      const hasUpdate = result.updateInfo 
        ? this.compareVersions(result.updateInfo.version, this.currentVersion) > 0
        : false
        
      logger.info('Version comparison result:', {
        currentVersion: this.currentVersion,
        latestVersion: result.updateInfo?.version,
        hasUpdate
      })

      return {
        hasUpdate,
        currentVersion: this.currentVersion,
        latestVersion: result.updateInfo ? {
          version: result.updateInfo.version,
          releaseNotes: (() => {
            let notes = Array.isArray(result.updateInfo.releaseNotes) 
              ? result.updateInfo.releaseNotes.map((note: any) => typeof note === 'string' ? note : note.note || '').join('\n')
              : (result.updateInfo.releaseNotes || '更新日志暂无')
            
            // 处理字符串中的转义字符
            if (typeof notes === 'string') {
              notes = notes.replace(/\\n/g, '\n')
            }
            return notes
          })(),
          releaseDate: result.updateInfo.releaseDate || new Date().toISOString(),
          downloadUrl: '',
          fileSize: result.updateInfo.files?.[0]?.size || 0,
          checksum: result.updateInfo.files?.[0]?.sha512 || ''
        } : undefined
      }

    } catch (error) {
      logger.error('Failed to check for updates:', error)
      const errorMsg = error instanceof Error ? error.message : 'Unknown error'
      
      this.emit('update-error', errorMsg)
      
      return {
        hasUpdate: false,
        currentVersion: this.currentVersion,
        error: errorMsg
      }
    } finally {
      this.isCheckingForUpdate = false
      this.isManualCheck = false // 重置标志
    }
  }

  // 下载更新
  async downloadUpdate(versionInfo: VersionInfo): Promise<string> {
    if (this.isDownloading) {
      throw new Error('Download already in progress')
    }

    if (this.status.status !== 'available') {
      throw new Error('No update available to download')
    }

    this.isDownloading = true
    this.emit('download-start', versionInfo)

    try {
      logger.info('Starting download for version:', versionInfo.version)
      
      // 使用官方方法开始下载
      await autoUpdater.downloadUpdate()
      
      // 返回一个占位符路径，实际文件由 autoUpdater 管理
      return 'managed-by-electron-updater'

    } catch (error) {
      logger.error('Download failed:', error)
      const errorMsg = error instanceof Error ? error.message : 'Download failed'
      this.emit('download-error', errorMsg)
      this.isDownloading = false
      throw error
    }
  }

  // 安装更新
  async installUpdate(): Promise<void> {
    if (this.status.status !== 'downloaded') {
      throw new Error('No update downloaded')
    }

    this.emit('install-start')

    try {
      logger.info('Starting installation')

      // 显示确认对话框
      const result = await dialog.showMessageBox({
        type: 'question',
        title: '安装更新',
        message: '即将安装新版本，应用将会关闭。是否继续？',
        buttons: ['安装', '取消'],
        defaultId: 0
      })

      if (result.response === 1) {
        logger.info('User cancelled installation')
        this.emit('install-cancelled')
        return
      }

      logger.info('Installing update and restarting...')
      this.emit('install-progress', '正在安装更新...')

      // 使用官方方法安装并重启
      autoUpdater.quitAndInstall()

    } catch (error) {
      logger.error('Installation failed:', error)
      const errorMsg = error instanceof Error ? error.message : 'Installation failed'
      this.emit('install-error', errorMsg)
      throw error
    }
  }

  // 获取当前状态
  getStatus(): UpdateStatus {
    return this.status
  }

  // 获取当前版本
  getCurrentVersion(): string {
    return this.currentVersion
  }

  // 版本比较（保持兼容性，但官方 autoUpdater 会自动处理）
  compareVersions(version1: string, version2: string): number {
    const v1Parts = version1.split('.').map(Number)
    const v2Parts = version2.split('.').map(Number)
    
    for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
      const v1Part = v1Parts[i] || 0
      const v2Part = v2Parts[i] || 0
      
      if (v1Part < v2Part) return -1
      if (v1Part > v2Part) return 1
    }
    
    return 0
  }

  // 清理下载文件（官方 autoUpdater 自动管理，这里保持接口兼容）
  async cleanupDownloads(): Promise<void> {
    logger.info('Cleanup is managed by electron-updater automatically')
  }
}

// 导出单例实例
let updateManager: UpdateManager | null = null

export function getUpdateManager(): UpdateManager {
  if (!updateManager) {
    updateManager = new UpdateManager()
  }
  return updateManager
}
